<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Job Type Selection - GigGenius</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/page1-2025.css') }}">
</head>
<body>


    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }}">
                    {{ message }}
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <form action="/save_page1" method="POST" id="jobTypeForm">
        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
        <div class="container">
            <!-- Progress Indicator -->
            <div class="progress-indicator">
                <div class="progress-step">
                    <div class="step-number active">1</div>
                    <div class="step-label active">Job Type</div>
                </div>
                <div class="step-connector"></div>
                <div class="progress-step">
                    <div class="step-number">2</div>
                    <div class="step-label">Job Details</div>
                </div>
                <div class="step-connector"></div>
                <div class="progress-step">
                    <div class="step-number">3</div>
                    <div class="step-label">Review & Post</div>
                </div>
            </div>

            <div class="title-section">
                <h1>How can we help you get started?</h1>
                <p>Choose how you want to create your job posting</p>
            </div>
            <div class="dropdown-section">
                <div class="job-post-dropdown" onclick="toggleDropdown()">
                    <span class="job-post-text">I want to create a new job post</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="dropdown-options" id="dropdownOptions">
                    <div class="option-box" onclick="selectOption('one-time', this)">
                        <input type="radio" name="job_type" value="one-time" hidden>
                        <i class="fas fa-calendar-day icon"></i>
                        <h3>One-time project</h3>
                        <p>A single project with a defined deliverable and end date.</p>
                        <div class="details">
                            <div><i class="fas fa-check"></i> Fixed scope</div>
                            <div><i class="fas fa-bullseye"></i> Clear deliverables</div>
                            <div><i class="fas fa-calendar-check"></i> Defined timeline</div>
                        </div>
                    </div>
                    <div class="option-box" onclick="selectOption('ongoing', this)">
                        <input type="radio" name="job_type" value="ongoing" hidden>
                        <i class="fas fa-sync icon"></i>
                        <h3>Ongoing collaboration</h3>
                        <p>Regular but flexible work schedule with no fixed end date.</p>
                        <div class="details">
                            <div><i class="fas fa-check"></i> Flexible schedule</div>
                            <div><i class="fas fa-handshake"></i> Long-term partnership</div>
                            <div><i class="fas fa-clock"></i> Hourly or retainer-based</div>
                        </div>
                    </div>
                </div>

                <!-- New Draft dropdown -->
                <div class="draft-container">
                    <div class="job-post-dropdown" onclick="toggleDropdown2()">
                        <span class="job-post-text">I want to continue editing a draft</span>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="dropdown-options" id="dropdownOptions2">
                        <div class="job-post-dropdown" onclick="toggleDropdown3()">
                            <span class="job-post-text" id="selectedDraftText">Select a draft to continue</span>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                    </div>
                </div>

                <!-- Separate container for draft options -->
                <div class="simple-dropdown-box" id="dropdownOptions3">
                    {% if draft_jobs %}
                        {% for draft in draft_jobs %}
                            <div class="draft-option" onclick="selectDraftOption(this, '{{ draft.title }}', true, {{ draft.id }})">
                                {{ draft.title }}
                                <span class="draft-date">{{ draft.created_at.strftime('%b %d, %Y') if draft.created_at else '' }}</span>
                            </div>
                        {% endfor %}
                    {% else %}
                        <div class="no-drafts">No draft jobs available</div>
                    {% endif %}
                </div>

                <!-- Add this after the draft container -->
                <div class="draft-container">
                    <div class="job-post-dropdown" onclick="toggleDropdown4()">
                        <span class="job-post-text">I want to rework a previous job post</span>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="dropdown-options" id="dropdownOptions4">
                        <div class="job-post-dropdown" onclick="toggleDropdown5()">
                            <span class="job-post-text" id="selectedWixText">Wix Website Optimization for All Devices (Quick Job)</span>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                    </div>
                </div>

                <!-- Add this for the nested dropdown options -->
                <div class="simple-dropdown-box" id="dropdownOptions5">
                    <div class="draft-option" onclick="selectWixOption(this, 'Web Developer')">Web Developer</div>
                    <div class="draft-option" onclick="selectWixOption(this, 'UI/UX Designer')">UI/UX Designer</div>
                    <div class="draft-option" onclick="selectWixOption(this, 'Full Stack Developer')">Full Stack Developer</div>
                    <div class="draft-option" onclick="selectWixOption(this, 'Mobile App Developer')">Mobile App Developer</div>
                    <div class="draft-option" onclick="selectWixOption(this, 'Wix Code Needed')">Wix Code Needed</div>
                    <div class="draft-option" onclick="selectWixOption(this, 'Virtual Assistant')">Virtual Assistant</div>
                    <div class="draft-option" onclick="selectWixOption(this, 'Lead Generation and Sales Specialist')">Lead Generation and Sales Specialist</div>
                    <div class="draft-option" onclick="selectWixOption(this, 'WordPress Plugin Developer')">WordPress Plugin Developer</div>
                    <div class="draft-option" onclick="selectWixOption(this, 'Social Media Manager')">Social Media Manager</div>
                    <div class="draft-option" onclick="selectWixOption(this, 'Content Writer & SEO Specialist')">Content Writer & SEO Specialist</div>
                    <div class="draft-option" onclick="selectWixOption(this, 'Email Marketing Expert')">Email Marketing Expert</div>
                    <div class="draft-option" onclick="selectWixOption(this, 'Shopify Store Developer')">Shopify Store Developer</div>
                    <div class="draft-option" onclick="selectWixOption(this, 'Data Entry Specialist')">Data Entry Specialist</div>
                    <div class="draft-option" onclick="selectWixOption(this, 'Graphic Designer')">Graphic Designer</div>
                    <div class="draft-option" onclick="selectWixOption(this, 'Customer Service Representative')">Customer Service Representative</div>
                    <div class="draft-option" onclick="selectWixOption(this, 'Project Manager')">Project Manager</div>
                    <div class="draft-option" onclick="selectWixOption(this, 'Digital Marketing Specialist')">Digital Marketing Specialist</div>
                </div>
            </div>
            <div class="button-container">
                <div class="buttons-wrapper">
                    <button type="button" class="cancel-button" onclick="cancelProcess()">Cancel</button>
                    <button type="button" class="continue-button" id="continueButton" disabled onclick="handleContinue()">Continue</button>
                </div>
            </div>

            <!-- Hidden input to store draft ID if selected -->
            <input type="hidden" id="selectedDraftId" name="draft_job_id" value="">
        </div>
    </form>



    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Add active class to the first dropdown by default
            const firstDropdown = document.querySelector('.job-post-dropdown');
            if (firstDropdown) {
                firstDropdown.classList.add('active');
                document.getElementById('dropdownOptions').classList.add('show');
            }

            // Select the first job type option by default
            const firstOption = document.querySelector('.option-box');
            if (firstOption) {
                // Simulate a click on the first option
                firstOption.click();

                // Make sure the radio is checked
                const radio = firstOption.querySelector('input[type="radio"]');
                if (radio) {
                    radio.checked = true;
                }
            }

            // Close dropdowns when clicking outside
            document.addEventListener('click', function(event) {
                if (!event.target.closest('.dropdown-section')) {
                    closeAllDropdowns();
                }
            });

            // Animate progress steps
            animateProgressSteps();
        });

        function animateProgressSteps() {
            const steps = document.querySelectorAll('.step-number');
            steps.forEach((step, index) => {
                setTimeout(() => {
                    animateElement(step, 'pulse');
                }, index * 300);
            });
        }

        function clearPreviousEditedValues() {
            // Find all localStorage keys that start with 'edited'
            Object.keys(localStorage).forEach(key => {
                if (key.startsWith('editedCategory_') || key.startsWith('editedTitle_')) {
                    localStorage.removeItem(key);
                }
            });
        }

        function selectOption(option, element) {
            // Save project type
            localStorage.setItem('projectType', option);
            localStorage.setItem('mode', 'create'); // optional

            // Clear any previous edited values when starting a new job
            clearPreviousEditedValues();

            // Visually mark the selected option
            document.querySelectorAll('.option-box').forEach(el => el.classList.remove('selected'));
            element.classList.add('selected');

            // Check the corresponding hidden radio input
            const input = element.querySelector('input[type="radio"]');
            if (input) {
                input.checked = true;
                console.log('Selected job_type:', input.value);
            }

            // Enable the Continue button
            document.getElementById('continueButton').disabled = false;

            // Add animation to the continue button
            animateElement(document.getElementById('continueButton'), 'pulse');
        }

        function toggleDropdown() {
            const dropdown = document.getElementById('dropdownOptions');
            const dropdownHeader = document.querySelector('.job-post-dropdown');

            // Close all other dropdowns first
            closeAllDropdowns();

            // Toggle this dropdown
            dropdown.classList.toggle('show');
            dropdownHeader.classList.toggle('active');
        }

        function toggleDropdown2() {
            const draftDropdown = document.querySelector('.draft-container .job-post-dropdown');
            const draftOptions = document.getElementById('dropdownOptions2');

            // Close all other dropdowns first
            closeAllDropdowns();

            // Toggle draft dropdown
            draftDropdown.classList.toggle('active');
            draftOptions.classList.toggle('show');
        }

        function toggleDropdown3() {
            const dropdown = document.querySelector('#dropdownOptions2 .job-post-dropdown');
            const options = document.getElementById('dropdownOptions3');

            // Position the dropdown correctly
            positionDropdown(options, dropdown);

            dropdown.classList.toggle('active');
            options.classList.toggle('show');
        }

        function selectDraftOption(element, text, enableContinue = false, draftId = null) {
            document.getElementById('selectedDraftText').textContent = text;
            localStorage.setItem('selectedJobTitle', text);
            localStorage.setItem('mode', 'draft');

            if (draftId) {
                localStorage.setItem('selectedDraftId', draftId);
            }

            // Mark the selected option
            document.querySelectorAll('.draft-option').forEach(el => el.classList.remove('selected'));
            element.classList.add('selected');

            document.getElementById('dropdownOptions3').classList.remove('show');
            document.getElementById('continueButton').disabled = false;

            // Add animation to the continue button
            animateElement(document.getElementById('continueButton'), 'pulse');

            // If a draft is selected, we'll redirect to page3 directly when the continue button is clicked
            if (draftId) {
                // Update the form action to go directly to page3 with the draft_job_id
                const form = document.querySelector('form');
                form.action = `/page3?draft_job_id=${draftId}`;
                form.method = 'GET';
            }
        }

        function toggleDropdown4() {
            const reworkDropdown = document.querySelector('.draft-container:nth-child(3) .job-post-dropdown');
            const reworkOptions = document.getElementById('dropdownOptions4');

            closeAllDropdowns();

            reworkDropdown.classList.toggle('active');
            reworkOptions.classList.toggle('show');
        }

        function toggleDropdown5() {
            const dropdown = document.querySelector('#dropdownOptions4 .job-post-dropdown');
            const options = document.getElementById('dropdownOptions5');

            // Position the dropdown correctly
            positionDropdown(options, dropdown);

            dropdown.classList.toggle('active');
            options.classList.toggle('show');
        }

        function selectWixOption(element, text) {
            document.getElementById('selectedWixText').textContent = text;
            localStorage.setItem('mode', 'rework');
            localStorage.setItem('selectedWixText', text);

            // Mark the selected option
            document.querySelectorAll('#dropdownOptions5 .draft-option').forEach(el => el.classList.remove('selected'));
            element.classList.add('selected');

            document.getElementById('dropdownOptions5').classList.remove('show');
            document.getElementById('continueButton').disabled = false;

            // Add animation to the continue button
            animateElement(document.getElementById('continueButton'), 'pulse');
        }

        function closeAllDropdowns() {
            const allDropdowns = [
                'dropdownOptions',
                'dropdownOptions2',
                'dropdownOptions3',
                'dropdownOptions4',
                'dropdownOptions5'
            ];

            allDropdowns.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.classList.remove('show');
                }
            });

            document.querySelectorAll('.job-post-dropdown').forEach(dropdown => {
                dropdown.classList.remove('active');
            });
        }

        function positionDropdown(dropdown, reference) {
            if (!dropdown || !reference) return;

            const rect = reference.getBoundingClientRect();
            dropdown.style.position = 'absolute';
            dropdown.style.top = `${rect.bottom + window.scrollY}px`;
            dropdown.style.left = `${rect.left}px`;
            dropdown.style.width = `${reference.offsetWidth}px`;
            dropdown.style.zIndex = '1010';
        }

        function animateElement(element, animation, duration = 600) {
            if (!element) return;

            // Add animation class
            element.classList.add(animation);

            // Remove animation class after duration
            setTimeout(() => {
                element.classList.remove(animation);
            }, duration);
        }

        function cancelProcess() {
            // Redirect to home or dashboard
            window.location.href = '/';
        }

        function handleContinue() {
            // Check if a draft was selected
            const draftId = localStorage.getItem('selectedDraftId');

            if (draftId) {
                // If a draft was selected, redirect to page3 with the draft_job_id
                window.location.href = `/page3?draft_job_id=${draftId}`;
            } else {
                // Otherwise, get the selected job type and redirect directly
                let selectedJobType = document.querySelector('input[name="job_type"]:checked');
                let jobTypeValue = 'one-time'; // Default value

                if (!selectedJobType) {
                    // If no job type is selected, select the first one
                    const firstJobType = document.querySelector('input[name="job_type"]');
                    if (firstJobType) {
                        firstJobType.checked = true;
                        jobTypeValue = firstJobType.value;
                        console.log('Auto-selected job_type:', jobTypeValue);
                    }
                } else {
                    jobTypeValue = selectedJobType.value;
                    console.log('User-selected job_type:', jobTypeValue);
                }

                // Create a visible indicator that we're navigating
                const submitIndicator = document.createElement('div');
                submitIndicator.style.position = 'fixed';
                submitIndicator.style.top = '50%';
                submitIndicator.style.left = '50%';
                submitIndicator.style.transform = 'translate(-50%, -50%)';
                submitIndicator.style.padding = '20px';
                submitIndicator.style.background = 'rgba(0, 74, 173, 0.8)';
                submitIndicator.style.color = 'white';
                submitIndicator.style.borderRadius = '10px';
                submitIndicator.style.zIndex = '9999';
                submitIndicator.style.fontFamily = 'Poppins, sans-serif';
                submitIndicator.textContent = 'Proceeding to next step...';
                document.body.appendChild(submitIndicator);

                // Get the CSRF token
                const csrfToken = document.querySelector('input[name="csrf_token"]').value;

                // Use fetch to submit the form data
                fetch('/save_page1', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-CSRFToken': csrfToken
                    },
                    body: `job_type=${jobTypeValue}&csrf_token=${csrfToken}`
                })
                .then(response => {
                    if (response.ok) {
                        // Redirect to page2 directly
                        window.location.href = '/page2';
                    } else {
                        console.error('Error submitting form:', response.statusText);
                        submitIndicator.textContent = 'Error: ' + response.statusText;
                        submitIndicator.style.background = 'rgba(220, 53, 69, 0.8)';
                    }
                })
                .catch(error => {
                    console.error('Error submitting form:', error);
                    submitIndicator.textContent = 'Error occurred. Redirecting...';
                    submitIndicator.style.background = 'rgba(255, 153, 0, 0.8)';

                    // Add a fallback redirect after a short delay
                    setTimeout(() => {
                        // Store job type in localStorage as a fallback
                        localStorage.setItem('fallback_job_type', jobTypeValue);

                        // Redirect directly to page2
                        window.location.href = '/page2';
                    }, 1500);
                });
            }
        }
    </script>

    <style>
        /* Animation styles */
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes fadeIn {
            0% { opacity: 0; transform: translateY(-10px); }
            100% { opacity: 1; transform: translateY(0); }
        }

        .pulse {
            animation: pulse 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .progress-indicator {
            animation: fadeIn 0.8s ease-out forwards;
        }

        .step-number {
            position: relative;
        }

        .step-number::after {
            content: '';
            position: absolute;
            top: -4px;
            left: -4px;
            right: -4px;
            bottom: -4px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-blue), var(--primary-pink));
            opacity: 0;
            z-index: -1;
            transition: opacity 0.3s ease;
        }

        .step-number.active::after {
            opacity: 0.3;
            animation: pulse 1.5s infinite;
        }

        /* Draft styling */
        .draft-date {
            font-size: 0.8rem;
            color: #777;
            margin-left: 10px;
            font-style: italic;
        }

        .no-drafts {
            padding: 15px;
            text-align: center;
            color: #666;
            font-style: italic;
        }

        .draft-option {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .draft-option.selected {
            background: linear-gradient(to right, rgba(0, 74, 173, 0.1), rgba(205, 32, 139, 0.1));
            color: var(--primary-blue);
            font-weight: 500;
        }

        /* Scrollable dropdown for many drafts */
        #dropdownOptions3 {
            max-height: 300px;
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: rgba(0, 74, 173, 0.5) rgba(0, 0, 0, 0.1);
        }

        #dropdownOptions3::-webkit-scrollbar {
            width: 6px;
        }

        #dropdownOptions3::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.05);
            border-radius: 10px;
        }

        #dropdownOptions3::-webkit-scrollbar-thumb {
            background: linear-gradient(to bottom, rgba(0, 74, 173, 0.5), rgba(205, 32, 139, 0.5));
            border-radius: 10px;
        }

        #dropdownOptions3::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(to bottom, rgba(0, 74, 173, 0.7), rgba(205, 32, 139, 0.7));
        }
    </style>
</body>
</html>
