
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>All Gig Posts | GigGenius</title>
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='img/logo.png') }}">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.2/font/bootstrap-icons.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #004AAD;
            --primary-pink: #CD208B;
            --yellow: #FFD700;
            --text-dark: #000000;
            --text-light: #FFFFFF;
            --text-gray: #666;
            --light-blue: #e0efff;
            --border-light: #eef2f8;
            --card-bg: #ffffff;
            --hover-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
        }

        html {
            font-size: 1rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            transition: all 0.3s ease-in-out;
        }

        body {
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
            background: linear-gradient(135deg,
                #f8f9ff 0%,
                #f0f5ff 35%,
                #e8f0ff 65%,
                #e5edff 100%
            );
            color: var(--text-dark);
            min-height: 100vh;
            font-size: 15px;
            line-height: 1.6;
        }

        /* Navbar Styles */
        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            height: 4.5rem;
            position: sticky;
            top: 0;
            z-index: 1000;
            flex-wrap: wrap;
            width: 100%;
        }

        /* Mobile menu button */
        .mobile-menu-btn {
            display: none;
            background: none;
            border: none;
            color: var(--primary-blue);
            font-size: 1.5rem;
            cursor: pointer;
            padding: 0.5rem;
        }

        .mobile-menu-btn:hover {
            color: var(--primary-pink);
        }

        .navbar-left {
            display: flex;
            align-items: center;
            gap: 0;
            padding-left: 1rem;
        }

        .logo {
            display: flex;
            align-items: center;
            color: var(--primary-pink);
            text-decoration: none;
            position: relative;
        }

        .logo img {
            width: 3.5rem;
            height: 3.5rem;
        }

        .logo h1 {
            font-size: 1.5rem;
            font-weight: bold;
            margin-left: 0.5rem;
            margin-right: 0.5rem;
            color: var(--primary-pink);
        }

        .logo:hover, .logo:active {
            color: var(--primary-blue);
        }

        .logo:hover h1 {
            color: var(--primary-blue);
        }

        /* Remove any pseudo-elements that might create lines */
        .logo:before, .logo:after {
            display: none !important;
        }

        /* Ensure no underlines or borders appear */
        .no-underline {
            text-decoration: none !important;
            border-bottom: none !important;
            box-shadow: none !important;
            position: relative;
        }

        .no-underline:after,
        .no-underline:before {
            display: none !important;
            content: none !important;
            border: none !important;
            text-decoration: none !important;
            box-shadow: none !important;
        }

        .nav-links {
            display: flex;
            gap: 0;
            align-items: center;
            height: 100%;
            margin: 0;
        }

        .nav-links a {
            color: var(--primary-blue);
            text-decoration: none;
            padding: 0.5rem 0.5rem;
            font-size: 1rem;
            font-weight: 500;
            position: relative;
            white-space: nowrap;
        }

        .nav-links a:hover, .nav-links a.active {
            color: var(--primary-pink);
        }

        .nav-links a:after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: 0;
            left: 0;
            background-color: var(--primary-pink);
            transition: width 0.3s ease;
        }

        .nav-links a:hover:after, .nav-links a.active:after {
            width: 100%;
        }

        .nav-dropdown {
            position: relative;
            display: inline-block;
            margin: 0;
        }

        .nav-dropbtn {
            font-weight: 500;
            font-size: 1rem;
            color: var(--primary-blue);
            background: none;
            border: none;
            padding: 0.5rem 0.5rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.3rem;
            position: relative;
            white-space: nowrap;
        }

        .nav-dropbtn:hover {
            color: var(--primary-pink);
        }

        .nav-dropbtn:after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: 0;
            left: 0;
            background-color: var(--primary-pink);
            transition: width 0.3s ease;
        }

        .nav-dropbtn:hover:after {
            width: 100%;
        }

        .right-section {
            display: flex;
            align-items: center;
            gap: 1.5rem;
            padding-right: 1rem;
        }

        /* Profile dropdown */
        .profile-dropdown {
            position: relative;
            display: inline-block;
        }

        .profile-button {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            overflow: hidden;
            cursor: pointer;
            border: 2px solid var(--primary-blue);
        }

        .profile-button img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
        }

        .profile-dropdown-content {
            display: none;
            position: absolute;
            right: 0;
            top: 50px;
            background-color: #fff;
            min-width: 220px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            border-radius: 8px;
            z-index: 1001;
            border: 1px solid rgba(0,0,0,0.1);
        }

        .profile-dropdown-content a {
            color: var(--primary-blue);
            padding: 12px 16px;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 0.9rem;
        }

        .profile-dropdown-content a i {
            width: 20px;
            text-align: center;
        }

        .profile-dropdown-content a:hover {
            background-color: #f9f9f9;
            color: var(--primary-pink);
        }

        /* Main content area */
        .dashboard {
            display: flex;
            flex-direction: column;
            max-width: 1400px;
            margin: 20px auto;
            background-color: rgba(255, 255, 255, 0.98);
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            padding: 30px;
            position: relative;
            overflow: hidden;
        }

        .dashboard::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 5px;
            background: linear-gradient(to right, var(--primary-blue), var(--primary-pink));
        }

        .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 1px solid var(--border-light);
        }

        h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 700;
            color: var(--primary-blue);
            position: relative;
            display: flex;
            align-items: center;
        }

        h1::before {
            content: '';
            display: inline-block;
            width: 6px;
            height: 28px;
            background: linear-gradient(to bottom, var(--primary-blue), var(--primary-pink));
            margin-right: 15px;
            border-radius: 3px;
        }

        h1::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 0;
            width: 40px;
            height: 3px;
            background-color: var(--primary-pink);
            border-radius: 2px;
            animation: width-expand 1s ease-out forwards;
        }

        @keyframes width-expand {
            from {
                width: 0;
            }
            to {
                width: 40px;
            }
        }

        /* Post job button */
        .post-job-btn {
            padding: 12px 24px;
            background: linear-gradient(to right, var(--primary-pink), #e91e63);
            color: white;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 10px rgba(194, 24, 91, 0.2);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .post-job-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(194, 24, 91, 0.3);
        }

        .post-job-btn:active {
            transform: translateY(0);
            box-shadow: 0 2px 5px rgba(194, 24, 91, 0.2);
        }

        /* Search and filter section */
        .search-bar {
            display: flex;
            margin-bottom: 25px;
            gap: 15px;
            position: relative;
        }

        .search-container {
            flex: 1;
            position: relative;
            display: flex;
            align-items: center;
        }

        .search-container i {
            position: absolute;
            left: 18px;
            color: var(--primary-blue);
            font-size: 16px;
        }

        .search-bar input {
            flex: 1;
            padding: 14px 18px 14px 45px;
            border: 2px solid #e0e7ff;
            border-radius: 10px;
            font-size: 15px;
            width: 100%;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            font-family: 'Poppins', sans-serif;
        }

        .search-bar input:focus {
            border-color: var(--primary-blue);
            box-shadow: 0 4px 12px rgba(6, 77, 172, 0.1);
            outline: none;
        }

        .search-bar input::placeholder {
            color: #a0aec0;
        }

        .filter-button {
            padding: 0 20px;
            height: 50px;
            background-color: #ffffff;
            border: 2px solid var(--primary-blue);
            border-radius: 10px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 10px;
            color: var(--primary-blue);
            font-size: 15px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        }

        .filter-button i {
            font-size: 16px;
        }

        .filter-button:hover {
            background-color: #f0f7ff;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(6, 77, 172, 0.1);
        }

        .filter-button.active {
            background-color: var(--primary-blue);
            color: #ffffff;
            box-shadow: 0 4px 12px rgba(6, 77, 172, 0.2);
        }

        .filter-count {
            background-color: var(--primary-pink);
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 11px;
            font-weight: 600;
            margin-left: 5px;
            animation: bounce 0.5s ease;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-5px);
            }
            60% {
                transform: translateY(-2px);
            }
        }

        /* Ripple effect for buttons */
        .filter-button {
            position: relative;
            overflow: hidden;
        }

        .ripple {
            position: absolute;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.4);
            transform: scale(0);
            animation: ripple-animation 0.5s linear;
            pointer-events: none;
        }

        @keyframes ripple-animation {
            to {
                transform: scale(2.5);
                opacity: 0;
            }
        }

        .filters {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
            margin-bottom: 24px;
            display: none;
            animation: slideDown 0.3s ease-out;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .filters.show {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }

        .filter-group {
            flex: 1;
            min-width: 200px;
        }

        .filter-group label {
            display: block;
            font-size: 13px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .filter-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            color: #1f2937;
            background-color: white;
            transition: all 0.2s ease;
            cursor: pointer;
            appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%236B7280' viewBox='0 0 16 16'%3E%3Cpath d='M8 10.5l-4-4h8l-4 4z'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 12px center;
        }

        .filter-group select:hover {
            border-color: #064dac;
        }

        .filter-group select:focus {
            outline: none;
            border-color: #064dac;
            box-shadow: 0 0 0 3px rgba(6, 77, 172, 0.1);
        }

        /* Enhanced filter tags */
        .filter-tag {
            background: #f0f7ff;
            border: 1px solid #064dac;
            padding: 8px 12px;
            border-radius: 20px;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-size: 13px;
            color: #064dac;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .filter-tag:hover {
            background: #e0efff;
        }

        .filter-tag .remove-tag {
            width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background: #064dac;
            color: white;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .filter-tag .remove-tag:hover {
            background: #d32f2f;
            transform: scale(1.1);
        }

        .actions {
            margin-top: 16px;
            display: none; /* Hide by default */
        }

        .actions.show {
            display: block;
            animation: fadeIn 0.3s ease-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .post-job-btn {
            padding: 10px 20px;
            background-color: #c2185b;
            color: white;
            border: none;
            border-radius: 4px;
            font-weight: 500;
            cursor: pointer;
        }

        .post-job-btn:hover {
            background-color: #c2185b;
            opacity: 0.9;
        }

        .status-tabs {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .selected-filters {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        /* Enhanced clear filters button */
        .clear-filters {
            padding: 10px 16px;
            background: #fff;
            border: 2px solid #e5e7eb;
            border-radius: 20px;
            color: #4b5563;
            font-weight: 500;
            transition: all 0.2s ease;
            display: none; /* Hide by default */
        }

        .clear-filters:hover {
            background: #f9fafb;
            border-color: #d1d5db;
            color: #1f2937;
        }

        .clear-filters.show {
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }

        .clear-filters.show::before {
            content: '×';
            font-size: 18px;
            font-weight: bold;
        }

        /* Job listings */
        .job-list {
            margin-top: 30px;
            position: relative;
        }

        .job-list-empty {
            text-align: center;
            padding: 60px 0;
            color: var(--light-text);
        }

        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 60px 0;
        }

        .empty-state-icon {
            font-size: 60px;
            color: #e0e7ff;
            margin-bottom: 20px;
        }

        .empty-state-text {
            font-size: 18px;
            color: var(--light-text);
            margin-bottom: 25px;
        }

        .job-card {
            background: linear-gradient(135deg, #ffffff, #fafcff);
            border: 1px solid var(--border-light);
            border-radius: 16px;
            padding: 28px;
            margin-bottom: 24px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            animation: card-fade-in 0.5s ease-out;
        }

        @keyframes card-fade-in {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .job-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(to bottom, var(--primary-blue), var(--primary-pink));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .job-card:hover {
            box-shadow: var(--hover-shadow);
            transform: translateY(-3px);
            border-color: #d4e2ff;
        }

        .job-card:hover::before {
            opacity: 1;
        }

        /* Add staggered animation for job cards */
        .job-card:nth-child(2) {
            animation-delay: 0.1s;
        }

        .job-card:nth-child(3) {
            animation-delay: 0.2s;
        }

        .job-card:nth-child(4) {
            animation-delay: 0.3s;
        }

        .job-card:nth-child(5) {
            animation-delay: 0.4s;
        }

        .job-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--primary-blue);
            margin-bottom: 12px;
            transition: color 0.2s ease;
            display: inline-block;
        }

        .job-title:hover {
            color: var(--primary-pink);
        }

        .job-meta {
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }

        .job-info {
            color: var(--light-text);
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .job-info i {
            color: var(--primary-pink);
            font-size: 14px;
            opacity: 0.8;
        }

        /* Enhanced job stats styling */
        .stats-actions-wrapper {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 15px;
        }

        .job-stats {
            flex: 1;
        }

        .stats-group {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }

        .stat-item {
            display: flex;
            align-items: center;
            color: var(--light-text);
            font-size: 14px;
        }

        .stat-number {
            font-weight: 600;
            color: var(--primary-blue);
            margin-right: 5px;
        }

        .new-badge {
            background-color: #ebf8ff;
            color: #3182ce;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            margin-left: 6px;
            border: 1px solid #bee3f8;
        }

        .hired-group {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        /* Job actions styling */
        .job-actions {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        /* Add tooltip for action buttons */
        .job-actions button {
            position: relative;
        }

        .job-actions button::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: -35px;
            left: 50%;
            transform: translateX(-50%);
            background-color: #2d3748;
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.2s ease;
            z-index: 100;
        }

        .job-actions button:hover::after {
            opacity: 1;
            visibility: visible;
        }

        .job-status {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
            transition: all 0.2s ease;
        }

        .status-badge.filled {
            background-color: #ebf8ff;
            color: #3182ce;
            border: 1px solid #bee3f8;
        }

        .status-badge.closed {
            background-color: #fff5f5;
            color: #e53e3e;
            border: 1px solid #fed7d7;
        }

        .status-badge.open {
            background-color: #f0fff4;
            color: #38a169;
            border: 1px solid #c6f6d5;
        }

        .status-badge.draft {
            background-color: #f7fafc;
            color: #718096;
            border: 1px solid #e2e8f0;
        }

        .status-badge i {
            font-size: 10px;
        }

        .status-badge:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
        }

        .job-stats {
            display: flex;
            gap: 32px;
        }

        .stats-group {
            display: flex;
            align-items: center;
            gap: 32px;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 6px;
            color: #4a5568;
            font-size: 14px;
        }

        .hired-group {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .stat-number {
            font-weight: 600;
            color: #2d3748;
        }

        .new-badge {
            background-color: #ebf5ff;
            color: #064dac;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-date {
            color: #666;
            font-size: 14px;
            margin-left: auto;
            padding-left: 24px;
        }

        .stats-actions-wrapper {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 16px;
            border-top: 1px solid #eef2f6;
        }

        .job-actions {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .reuse-posting-btn, .edit-draft-btn {
            background: linear-gradient(to right, var(--primary-pink), #e91e63);
            color: white;
            border: none;
            padding: 10px 18px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 3px 8px rgba(194, 24, 91, 0.15);
        }

        .reuse-posting-btn i, .edit-draft-btn i {
            font-size: 14px;
        }

        .reuse-posting-btn:hover, .edit-draft-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 12px rgba(194, 24, 91, 0.25);
        }

        .reuse-posting-btn:active, .edit-draft-btn:active {
            transform: translateY(0);
            box-shadow: 0 2px 5px rgba(194, 24, 91, 0.15);
        }

        .more-options-btn {
            background-color: #ffffff;
            color: var(--primary-blue);
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 10px;
            height: 40px;
            width: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .more-options-btn:hover {
            background-color: #f8fafc;
            border-color: #cbd5e0;
            transform: translateY(-2px);
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.05);
        }

        .more-options-btn i {
            color: var(--primary-blue);
            font-size: 16px;
        }

        /* Pagination styles */
        .pagination-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 40px;
            padding-top: 24px;
            border-top: 1px solid var(--border-light);
            animation: fade-in 0.5s ease-out 0.3s both;
        }

        @keyframes fade-in {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }

        .pagination-info {
            color: var(--light-text);
            font-size: 14px;
            font-weight: 500;
            background-color: #f8fafc;
            padding: 8px 16px;
            border-radius: 20px;
            border: 1px solid #e2e8f0;
        }

        .pagination-info span {
            color: var(--primary-blue);
            font-weight: 600;
        }

        .pagination-controls {
            display: flex;
            gap: 12px;
        }

        .pagination-button {
            padding: 12px 24px;
            border: 2px solid #e2e8f0;
            background: white;
            border-radius: 10px;
            color: var(--primary-blue);
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
            position: relative;
            overflow: hidden;
        }

        .pagination-button i {
            font-size: 12px;
            transition: transform 0.3s ease;
        }

        .pagination-button:first-child i {
            margin-right: 4px;
        }

        .pagination-button:last-child i {
            margin-left: 4px;
        }

        .pagination-button:hover:not(:disabled) {
            background-color: #f0f7ff;
            border-color: var(--primary-blue);
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(6, 77, 172, 0.1);
        }

        .pagination-button:hover:not(:disabled):first-child i {
            transform: translateX(-3px);
        }

        .pagination-button:hover:not(:disabled):last-child i {
            transform: translateX(3px);
        }

        .pagination-button:active:not(:disabled) {
            transform: translateY(0);
            box-shadow: 0 2px 5px rgba(6, 77, 172, 0.1);
        }

        .pagination-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            border-color: #e2e8f0;
            color: #a0aec0;
        }

        /* Add ripple effect to pagination buttons */
        .pagination-button .ripple {
            background-color: rgba(6, 77, 172, 0.1);
        }

        /* Filter select styling */
        .filter-select {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            font-family: 'Poppins', sans-serif;
            font-size: 14px;
            color: var(--dark-text);
            background-color: white;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23064dac' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 15px center;
            background-size: 15px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .filter-select:focus {
            outline: none;
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 3px rgba(6, 77, 172, 0.1);
        }

        .filter-select:hover {
            border-color: #cbd5e0;
        }

        /* Empty state styling */
        .no-jobs-found {
            text-align: center;
            padding: 80px 0;
            color: var(--light-text);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            animation: fade-in-up 0.6s ease-out;
            background: linear-gradient(135deg, #ffffff, #f8faff);
            border-radius: 16px;
            border: 1px dashed #d4e2ff;
            margin: 20px 0;
        }

        @keyframes fade-in-up {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .no-jobs-found i {
            font-size: 70px;
            color: #c2d5f2;
            margin-bottom: 25px;
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-10px);
            }
            100% {
                transform: translateY(0px);
            }
        }

        .no-jobs-found h3 {
            font-size: 24px;
            color: var(--primary-blue);
            margin-bottom: 15px;
            font-weight: 600;
        }

        .no-jobs-found p {
            font-size: 16px;
            color: var(--light-text);
            margin-bottom: 30px;
            max-width: 500px;
            line-height: 1.6;
        }

        .no-jobs-found .post-job-btn {
            padding: 14px 28px;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .no-jobs-found .post-job-btn:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 8px 20px rgba(194, 24, 91, 0.3);
        }

        /* No results found state */
        .no-results-found {
            text-align: center;
            padding: 60px 0;
            color: var(--light-text);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            animation: fade-in-up 0.6s ease-out;
            background: linear-gradient(135deg, #ffffff, #f8faff);
            border-radius: 16px;
            border: 1px dashed #d4e2ff;
            margin: 20px 0;
        }

        .no-results-found i {
            font-size: 60px;
            color: #c2d5f2;
            margin-bottom: 20px;
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 0.8;
            }
            50% {
                transform: scale(1.1);
                opacity: 1;
            }
            100% {
                transform: scale(1);
                opacity: 0.8;
            }
        }

        .no-results-found h3 {
            font-size: 22px;
            color: var(--primary-blue);
            margin-bottom: 12px;
            font-weight: 600;
        }

        .no-results-found p {
            font-size: 15px;
            color: var(--light-text);
            margin-bottom: 25px;
            max-width: 450px;
            line-height: 1.6;
        }

        .clear-filters-btn {
            padding: 12px 24px;
            background: linear-gradient(to right, var(--primary-blue), #3182ce);
            color: white;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 10px rgba(6, 77, 172, 0.2);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .clear-filters-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(6, 77, 172, 0.3);
        }

        .clear-filters-btn:active {
            transform: translateY(0);
            box-shadow: 0 2px 5px rgba(6, 77, 172, 0.2);
        }

        /* Responsive adjustments */
        @media (max-width: 1200px) {
            .nav-links {
                gap: 0.5rem;
            }

            .nav-links a {
                padding: 0.5rem 0.7rem;
                font-size: 1rem;
            }
        }

        @media (max-width: 992px) {
            .navbar-left {
                padding-left: 0.5rem;
            }

            .right-section {
                padding-right: 0.5rem;
            }

            .logo img {
                width: 3.2rem;
                height: 3.2rem;
            }

            .logo h1 {
                font-size: 1.4rem;
            }

            .nav-links {
                display: none;
            }

            .mobile-menu-btn {
                display: block;
                margin-left: 0.5rem;
            }

            .nav-links.active {
                display: flex;
                flex-direction: column;
                position: absolute;
                top: 4.5rem;
                left: 0;
                right: 0;
                background: white;
                box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                z-index: 1000;
                padding: 0.5rem 0;
                width: 100%;
                align-items: flex-start;
                margin: 0;
            }

            .nav-links.active a {
                width: 100%;
                padding: 0.7rem 1.2rem;
                margin: 0;
            }
        }

        @media (max-width: 480px) {
            .navbar-left {
                padding-left: 0.3rem;
            }

            .right-section {
                padding-right: 0.3rem;
                gap: 0.8rem;
            }

            .logo img {
                width: 3rem;
                height: 3rem;
            }

            .logo h1 {
                font-size: 1.2rem;
                margin-left: 0.3rem;
                margin-right: 0.3rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar">
        <div class="navbar-left">
            <a href="{{ url_for('landing_page') }}" style="text-decoration: none;">
                <div class="logo no-underline">
                    <img src="{{ url_for('static', filename='img/giggenius_logo.jpg') }}" alt="GigGenius Logo">
                    <h1 class="no-underline">GigGenius</h1>
                </div>
            </a>
            <button class="mobile-menu-btn" id="mobileMenuBtn">
                <i class="fas fa-bars"></i>
            </button>
            <div class="nav-links" id="navLinks">
                {% if session.get('user_type') == 'genius' %}
                <a href="{{ url_for('genius_page') }}">Dashboard</a>
                <a href="{{ url_for('find_gigs') }}">Find Gigs</a>
                <a href="{{ url_for('applications') }}">Applications</a>
                {% else %}
                <a href="{{ url_for('client_page') }}">Dashboard</a>
                <a href="{{ url_for('client_jobs') }}" class="active">My Jobs</a>
                <a href="{{ url_for('applications') }}">Applications</a>
                {% endif %}
                <a href="{{ url_for('messages') }}">Messages</a>
            </div>
        </div>
        <div class="right-section">
            <div class="profile-dropdown">
                <div class="profile-button">
                    <img src="{{ url_for('api_profile_photo', user_type=session.get('user_type'), user_id=session.get('user_id')) }}" alt="User">
                </div>
                <div class="profile-dropdown-content">
                    {% if session.get('user_type') == 'genius' %}
                    <a href="{{ url_for('genius_page') }}"><i class="fas fa-user"></i> My Profile</a>
                    {% else %}
                    <a href="{{ url_for('client_page') }}"><i class="fas fa-user"></i> My Profile</a>
                    {% endif %}
                    <a href="#"><i class="fas fa-cog"></i> Settings</a>
                    <div class="dropdown-divider"></div>
                    <a href="{{ url_for('logout') }}" class="logout-option"><i class="fas fa-sign-out-alt"></i> Logout</a>
                </div>
            </div>
        </div>
    </nav>

    <div class="dashboard">
        <div class="container">
            <h1>All job posts</h1>
            <button class="post-job-btn" onclick="window.location.href='{{ url_for('page1') }}'">
                <i class="fas fa-plus-circle"></i>
                Post a new job
            </button>
        </div>

        <div class="search-bar">
            <div class="search-container">
                <i class="fas fa-search"></i>
                <input type="text" placeholder="Search job postings by title or date..." id="searchInput">
            </div>
            <button class="filter-button" data-tooltip="Show/hide filters">
                <i class="fas fa-filter"></i>
                Filters
                <span class="filter-count" style="display: none;">0</span>
            </button>
        </div>

        <div class="filters">
            <div class="filter-group">
                <label><i class="fas fa-user-friends"></i> Posted by</label>
                <select class="filter-select">
                    <option>All coworkers</option>
                    <option>Me</option>
                    <option>My team</option>
                </select>
            </div>

            <div class="filter-group">
                <label><i class="fas fa-eye"></i> Visibility</label>
                <select class="filter-select">
                    <option>All</option>
                    <option>Invite-only</option>
                    <option>Public</option>
                </select>
            </div>

            <div class="filter-group">
                <label><i class="fas fa-tasks"></i> Status</label>
                <select class="filter-select">
                    <option>All</option>
                    <option>Drafts</option>
                    <option>Open</option>
                    <option>Filled</option>
                    <option>Closed</option>
                </select>
            </div>

            <div class="filter-group">
                <label><i class="fas fa-dollar-sign"></i> Type</label>
                <select class="filter-select">
                    <option>All</option>
                    <option>Fixed-price</option>
                    <option>Hourly</option>
                </select>
            </div>
        </div>

        <div class="actions">
            <div class="status-tabs">
                <div class="selected-filters">
                    <!-- Filter tags will be added here dynamically -->
                </div>
                <button class="clear-filters">Clear all filters</button>
            </div>
        </div>
        <div class="job-list">
            <!-- Job cards will be displayed here -->
            {% if job_posts|length > 0 %}
                {% for job in job_posts %}
                <div class="job-card">
                    <div class="job-title" onclick="window.location.href='{{ url_for('job_details', job_id=job.id) }}';" style="cursor: pointer;">{{ job.title }}</div>
                    <div class="job-meta">
                        <div class="job-info"><i class="far fa-calendar-alt"></i> Created {{ job.created_at }} by You</div>
                    </div>
                    <div class="stats-actions-wrapper">
                        <div class="job-stats">
                            <div class="stats-group">
                                <div class="stat-item">
                                    <span class="stat-number">{{ job.proposals_count }}</span> Proposals
                                    {% if job.new_proposals_count > 0 %}
                                    <span class="new-badge">{{ job.new_proposals_count }} new</span>
                                    {% endif %}
                                </div>
                                <div class="stat-item">
                                    <span class="stat-number">{{ job.messaged_count }}</span> Messaged
                                </div>
                                <div class="hired-group">
                                    <div class="stat-item">
                                        <span class="stat-number">{{ job.hired_count }}</span> Hired
                                    </div>
                                    <span class="status-badge status-{{ job.status|lower }}">{{ job.status }} - {{ job.status_date }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="job-actions">
                            {% if job.status == 'Filled' or job.status == 'Closed' %}
                            <button class="reuse-posting-btn" onclick="window.location.href='{{ url_for('page1', reuse_job_id=job.id) }}';" data-tooltip="Create a new job based on this posting">
                                <i class="fas fa-sync-alt"></i> Reuse Posting
                            </button>
                            {% else %}
                            <button class="edit-draft-btn" onclick="window.location.href='{{ url_for('page1', edit_job_id=job.id) }}';" data-tooltip="Continue editing this draft">
                                <i class="fas fa-edit"></i> Edit Draft
                            </button>
                            {% endif %}
                            <button class="more-options-btn" data-job-id="{{ job.id }}" data-tooltip="More actions">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                        </div>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="no-jobs-found">
                    <i class="fas fa-clipboard-list"></i>
                    <h3>No job posts found</h3>
                    <p>You haven't created any job posts yet. Click the button below to post your first job.</p>
                    <button class="post-job-btn" onclick="window.location.href='{{ url_for('page1') }}'">
                        <i class="fas fa-plus-circle"></i>
                        Post a new job
                    </button>
                </div>
            {% endif %}

            <!-- Pagination -->
            <div class="pagination-container" {% if job_posts|length == 0 %}style="display: none;"{% endif %}>
                <div class="pagination-info">
                    Showing <span id="showing-count">1-{{ job_posts|length }}</span> of <span id="total-count">{{ job_posts|length }}</span> job posts
                </div>
                <div class="pagination-controls">
                    <button class="pagination-button" disabled>
                        <i class="fas fa-chevron-left"></i> Previous
                    </button>
                    <button class="pagination-button" {% if job_posts|length < 10 %}disabled{% endif %}>
                        Next <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>

            <!-- No results found state (hidden by default) -->
            <div class="no-results-found" style="display: none;">
                <i class="fas fa-search"></i>
                <h3>No matching jobs found</h3>
                <p>Try adjusting your search or filter criteria to find what you're looking for.</p>
                <button class="clear-filters-btn" onclick="clearAllFilters()">Clear all filters</button>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const filterButton = document.querySelector('.filter-button');
            const filtersSection = document.querySelector('.filters');
            const actionsSection = document.querySelector('.actions');
            const selectedFiltersContainer = document.querySelector('.selected-filters');
            const clearFiltersBtn = document.querySelector('.clear-filters');
            const searchInput = document.getElementById('searchInput');
            let activeFilters = new Set();

            // Add sticky behavior to filters when scrolling
            const dashboard = document.querySelector('.dashboard');
            const filtersTop = filtersSection ? filtersSection.offsetTop : 0;

            window.addEventListener('scroll', function() {
                if (filtersSection.classList.contains('show')) {
                    if (window.pageYOffset > filtersTop) {
                        filtersSection.style.position = 'sticky';
                        filtersSection.style.top = '80px';
                        filtersSection.style.zIndex = '100';
                        filtersSection.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.1)';
                    } else {
                        filtersSection.style.position = 'relative';
                        filtersSection.style.top = '0';
                        filtersSection.style.boxShadow = 'none';
                    }
                }
            });

            // Enhanced filter button animation with ripple effect
            filterButton.addEventListener('click', function(e) {
                // Create ripple effect
                const ripple = document.createElement('span');
                ripple.classList.add('ripple');
                this.appendChild(ripple);

                const rect = this.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                ripple.style.width = ripple.style.height = `${size}px`;
                ripple.style.left = `${e.clientX - rect.left - size/2}px`;
                ripple.style.top = `${e.clientY - rect.top - size/2}px`;

                ripple.classList.add('active');

                setTimeout(() => {
                    ripple.remove();
                }, 500);

                // Toggle filter section
                filtersSection.classList.toggle('show');
                actionsSection.classList.toggle('show');
                filterButton.classList.toggle('active');

                if (filterButton.classList.contains('active')) {
                    filterButton.style.background = 'var(--primary-blue)';
                    filterButton.style.color = 'white';
                    filterButton.style.borderColor = 'var(--primary-blue)';
                } else {
                    filterButton.style.background = '#ffffff';
                    filterButton.style.color = 'var(--primary-blue)';
                    filterButton.style.borderColor = 'var(--primary-blue)';
                }
            });

            // Function to filter job cards based on selection
            function filterJobCards(select) {
                const value = select.value;
                const jobCards = document.querySelectorAll('.job-card');

                jobCards.forEach(card => {
                    const hasReuseButton = card.querySelector('.reuse-posting-btn') !== null;
                    const hasEditButton = !hasReuseButton; // If no reuse button, assume it has edit button

                    let shouldShow = true;

                    // Show cards with reuse button
                    if (['Me', 'My team', 'Invite-only', 'Filled', 'Closed', 'Fixed-price'].includes(value)) {
                        shouldShow = hasReuseButton;
                    }
                    // Show cards with edit button
                    else if (['Public', 'Drafts', 'Open', 'Hourly'].includes(value)) {
                        shouldShow = hasEditButton;
                    }
                    // Show all cards
                    else if (['All coworkers', 'All'].includes(value)) {
                        shouldShow = true;
                    }

                    if (shouldShow) {
                        card.style.display = '';
                        card.style.animation = 'fadeIn 0.3s ease-in-out';
                    } else {
                        card.style.display = 'none';
                    }
                });

                updateJobCount();
            }

            // Enhanced select changes with animation
            document.querySelectorAll('.filter-select').forEach(select => {
                select.addEventListener('change', function() {
                    const label = this.parentElement.querySelector('label').textContent;
                    const value = this.value;

                    filterJobCards(this);

                    if (value !== 'All' && value !== 'All coworkers') {
                        addFilterTag(label, value);
                        activeFilters.add(label);
                    } else {
                        removeFilterByLabel(label);
                        activeFilters.delete(label);
                    }

                    updateClearFiltersVisibility();
                    animateFilterChange();
                });
            });

            // Search functionality with no results handling
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                const jobCards = document.querySelectorAll('.job-card');
                const noResultsFound = document.querySelector('.no-results-found');
                let visibleCount = 0;

                jobCards.forEach(card => {
                    const title = card.querySelector('.job-title').textContent.toLowerCase();
                    const info = card.querySelector('.job-info').textContent.toLowerCase();

                    if (title.includes(searchTerm) || info.includes(searchTerm)) {
                        card.style.display = '';
                        card.style.animation = 'fadeIn 0.3s ease-in-out';
                        visibleCount++;
                    } else {
                        card.style.display = 'none';
                    }
                });

                // Show/hide no results message
                if (visibleCount === 0 && searchTerm.length > 0) {
                    noResultsFound.style.display = 'flex';
                    document.querySelector('.pagination-container').style.display = 'none';
                } else {
                    noResultsFound.style.display = 'none';
                    document.querySelector('.pagination-container').style.display = 'flex';
                }

                updateJobCount();
            });

            function addFilterTag(label, value) {
                removeFilterByLabel(label);

                const tag = document.createElement('div');
                tag.className = 'filter-tag';
                tag.dataset.label = label;
                tag.innerHTML = `
                    ${label}: ${value}
                    <span class="remove-tag" onclick="removeFilterTag(this)">
                        <i class="fas fa-times"></i>
                    </span>
                `;

                // Add entrance animation
                tag.style.opacity = '0';
                tag.style.transform = 'translateY(-10px)';
                selectedFiltersContainer.appendChild(tag);

                // Trigger animation
                setTimeout(() => {
                    tag.style.opacity = '1';
                    tag.style.transform = 'translateY(0)';
                }, 50);
            }

            function removeFilterByLabel(label) {
                const existingTag = Array.from(selectedFiltersContainer.children)
                    .find(tag => tag.dataset.label === label);

                if (existingTag) {
                    // Add exit animation
                    existingTag.style.opacity = '0';
                    existingTag.style.transform = 'translateY(-10px)';
                    setTimeout(() => existingTag.remove(), 300);
                }
            }

            function animateFilterChange() {
                const jobCards = document.querySelectorAll('.job-card');
                jobCards.forEach(card => {
                    if (card.style.display !== 'none') {
                        card.style.transition = 'all 0.3s ease';
                        card.style.transform = 'scale(0.98)';
                        card.style.opacity = '0.8';

                        setTimeout(() => {
                            card.style.transform = 'scale(1)';
                            card.style.opacity = '1';
                        }, 300);
                    }
                });
            }

            function updateJobCount() {
                const visibleCards = Array.from(document.querySelectorAll('.job-card')).filter(card =>
                    card.style.display !== 'none'
                ).length;

                const totalCards = document.querySelectorAll('.job-card').length;
                const showingCountEl = document.getElementById('showing-count');
                const totalCountEl = document.getElementById('total-count');
                const paginationContainer = document.querySelector('.pagination-container');
                const noResultsFound = document.querySelector('.no-results-found');

                if (showingCountEl && totalCountEl) {
                    showingCountEl.textContent = visibleCards > 0 ? `1-${visibleCards}` : '0';
                    totalCountEl.textContent = totalCards;
                }

                // Show/hide pagination and no results message
                if (visibleCards === 0 && totalCards > 0) {
                    paginationContainer.style.display = 'none';
                    noResultsFound.style.display = 'flex';
                } else if (visibleCards > 0) {
                    paginationContainer.style.display = 'flex';
                    noResultsFound.style.display = 'none';
                }
            }

            // Enhanced clear filters with animation
            clearFiltersBtn.addEventListener('click', function() {
                const tags = Array.from(selectedFiltersContainer.children);

                // Animate all tags removal
                tags.forEach((tag, index) => {
                    setTimeout(() => {
                        tag.style.opacity = '0';
                        tag.style.transform = 'translateY(-10px)';
                    }, index * 100);
                });

                // Reset selects and clear container
                setTimeout(() => {
                    document.querySelectorAll('select').forEach(select => {
                        select.selectedIndex = 0;
                    });
                    selectedFiltersContainer.innerHTML = '';
                    activeFilters.clear();
                    updateClearFiltersVisibility();
                    animateFilterChange();

                    // Reset search
                    searchInput.value = '';
                    const event = new Event('input');
                    searchInput.dispatchEvent(event);
                }, tags.length * 100);
            });

            // Function to update clear filters button visibility
            function updateClearFiltersVisibility() {
                const filterCount = selectedFiltersContainer.children.length;
                const filterCountBadge = document.querySelector('.filter-count');

                if (filterCount > 0) {
                    clearFiltersBtn.classList.add('show');

                    // Update filter count badge
                    if (filterCountBadge) {
                        filterCountBadge.textContent = filterCount;
                        filterCountBadge.style.display = 'flex';
                    }
                } else {
                    clearFiltersBtn.classList.remove('show');

                    // Hide filter count badge
                    if (filterCountBadge) {
                        filterCountBadge.style.display = 'none';
                    }
                }
            }

            // Initialize job count
            updateJobCount();
        });

        // Global function to remove filter tags
        function removeFilterTag(element) {
            const tag = element.closest('.filter-tag');
            const label = tag.dataset.label;

            // Reset corresponding select element
            document.querySelectorAll('.filter-group').forEach(group => {
                if (group.querySelector('label').textContent === label) {
                    group.querySelector('select').selectedIndex = 0;

                    // Trigger change event
                    const event = new Event('change');
                    group.querySelector('select').dispatchEvent(event);
                }
            });

            tag.remove();

            // Update clear filters button visibility
            const clearFiltersBtn = document.querySelector('.clear-filters');
            const selectedFiltersContainer = document.querySelector('.selected-filters');
            if (selectedFiltersContainer.children.length === 0) {
                clearFiltersBtn.classList.remove('show');
            }
        }

        // Function to clear all filters and search
        function clearAllFilters() {
            // Clear search input
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.value = '';
                const inputEvent = new Event('input');
                searchInput.dispatchEvent(inputEvent);
            }

            // Reset all select elements
            document.querySelectorAll('.filter-select').forEach(select => {
                select.selectedIndex = 0;
                const changeEvent = new Event('change');
                select.dispatchEvent(changeEvent);
            });

            // Clear filter tags
            const selectedFiltersContainer = document.querySelector('.selected-filters');
            if (selectedFiltersContainer) {
                selectedFiltersContainer.innerHTML = '';

                // Update filter count badge
                const filterCountBadge = document.querySelector('.filter-count');
                if (filterCountBadge) {
                    filterCountBadge.style.display = 'none';
                }
            }

            // Hide no results message
            const noResultsFound = document.querySelector('.no-results-found');
            if (noResultsFound) {
                noResultsFound.style.display = 'none';
            }

            // Show pagination
            const paginationContainer = document.querySelector('.pagination-container');
            if (paginationContainer) {
                paginationContainer.style.display = 'flex';
            }

            // Show all job cards with animation
            const jobCards = document.querySelectorAll('.job-card');
            jobCards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.display = '';
                    card.style.animation = 'fadeIn 0.3s ease-out';
                }, index * 50); // Staggered animation
            });

            // Update job count
            setTimeout(updateJobCount, 100);
        }

        // Profile dropdown functionality
        document.addEventListener('DOMContentLoaded', function() {
            const profileButton = document.querySelector('.profile-button');
            const dropdownContent = document.querySelector('.profile-dropdown-content');

            if (profileButton && dropdownContent) {
                profileButton.addEventListener('click', function(e) {
                    e.stopPropagation();
                    dropdownContent.style.display = dropdownContent.style.display === 'block' ? 'none' : 'block';
                });

                // Close dropdown when clicking outside
                document.addEventListener('click', function(e) {
                    if (!profileButton.contains(e.target) && !dropdownContent.contains(e.target)) {
                        dropdownContent.style.display = 'none';
                    }
                });
            }

            // Add dropdown divider styling
            const dropdownDividers = document.querySelectorAll('.dropdown-divider');
            dropdownDividers.forEach(divider => {
                divider.style.borderTop = '1px solid #e9ecef';
                divider.style.margin = '0.5rem 0';
            });

            // Mobile menu toggle
            const mobileMenuBtn = document.getElementById('mobileMenuBtn');
            const navLinks = document.getElementById('navLinks');

            if (mobileMenuBtn) {
                mobileMenuBtn.addEventListener('click', function() {
                    navLinks.classList.toggle('active');

                    // Change icon based on menu state
                    const icon = this.querySelector('i');
                    if (navLinks.classList.contains('active')) {
                        icon.classList.remove('fa-bars');
                        icon.classList.add('fa-times');
                    } else {
                        icon.classList.remove('fa-times');
                        icon.classList.add('fa-bars');
                    }
                });
            }

            // Close mobile menu when clicking outside
            document.addEventListener('click', function(e) {
                if (window.innerWidth <= 992 &&
                    !navLinks.contains(e.target) &&
                    !mobileMenuBtn.contains(e.target) &&
                    navLinks.classList.contains('active')) {
                    navLinks.classList.remove('active');

                    // Reset icon
                    const icon = mobileMenuBtn.querySelector('i');
                    icon.classList.remove('fa-times');
                    icon.classList.add('fa-bars');
                }
            });

            // Handle window resize
            window.addEventListener('resize', function() {
                if (window.innerWidth > 992 && navLinks.classList.contains('active')) {
                    navLinks.classList.remove('active');

                    // Reset icon
                    const icon = mobileMenuBtn.querySelector('i');
                    icon.classList.remove('fa-times');
                    icon.classList.add('fa-bars');
                }
            });
        });
    </script>
</body>
</html>

