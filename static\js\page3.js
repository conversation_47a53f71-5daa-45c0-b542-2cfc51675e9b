// Store job details
let jobDetails = {
    title: document.getElementById('job-title').textContent,
    category: document.getElementById('job-category').textContent,
    specialty: document.getElementById('job-specialty').textContent,
    description: '',
    skills: [],
    scope: {
        description: '',
        size: '',
        duration: {
            value: 0,
            unit: ''
        },
        experienceLevel: '',
        hiringPreference: ''
    },
    budget: {
        type: 'fixed',
        amount: 0,
        min: 0,
        max: 0
    },
    job_type: 'One-time project'  // Default job type
};

// Animation effects
const animateElement = (element, animation, duration = 300) => {
    element.style.animation = `${animation} ${duration}ms forwards`;
    return new Promise(resolve => {
        setTimeout(() => {
            element.style.animation = '';
            resolve();
        }, duration);
    });
};

// Open modal with animation
function openEditModal(type) {
    const modalMap = {
        'title': 'titleModal',
        'category': 'categoryModal',
        'description': 'descriptionModal',
        'skills': 'skillsModal',
        'scope': 'scopeModal',
        'budget': 'budgetModal'
    };

    const modalId = modalMap[type];
    if (!modalId) return;

    const modal = document.getElementById(modalId);

    // Populate current values
    if (type === 'title') {
        document.getElementById('edit-title').value = jobDetails.title;
    } else if (type === 'category') {
        console.log("Opening category modal with current values:",
                   "Category:", jobDetails.category,
                   "Specialty:", jobDetails.specialty);

        // Set the category dropdown value
        const categorySelect = document.getElementById('edit-category');

        // Find the option with matching text content
        let categoryFound = false;
        for (let i = 0; i < categorySelect.options.length; i++) {
            if (categorySelect.options[i].textContent === jobDetails.category) {
                categorySelect.selectedIndex = i;
                categoryFound = true;
                console.log("Found matching category option at index:", i);
                break;
            }
        }

        // If category not found in dropdown, switch to custom input
        if (!categoryFound) {
            console.log("Category not found in dropdown, switching to custom input");
            toggleCategorySelectionType('custom');
            document.getElementById('custom-category-input').value = jobDetails.category;
        }

        // Initialize specialty dropdown based on current category
        updateSpecialties();

        // Set the specialty dropdown value if it exists
        const specialtySelect = document.getElementById('edit-specialty');
        if (jobDetails.specialty) {
            // Check if the specialty exists in the dropdown
            let found = false;
            const options = specialtySelect.querySelectorAll('option');
            for (const option of options) {
                if (option.textContent === jobDetails.specialty) {
                    option.selected = true;
                    found = true;
                    console.log("Found matching specialty option:", option.textContent);
                    break;
                }
            }

            // If specialty not found in dropdown, switch to custom input
            if (!found && jobDetails.specialty !== 'General') {
                console.log("Specialty not found in dropdown, switching to custom input");
                toggleSpecialtySelectionType('custom');
                document.getElementById('custom-specialty-input').value = jobDetails.specialty;
            }
        }
    } else if (type === 'description') {
        const descriptionText = document.getElementById('job-description').value;
        document.getElementById('edit-description').value = descriptionText;
    } else if (type === 'skills') {
        // Clear the input field
        document.getElementById('skills-input').value = '';

        // Clear existing skills in the edit list
        const skillsList = document.getElementById('edit-skills-list');
        skillsList.innerHTML = '';

        // Add current skills to the edit list
        jobDetails.skills.forEach(skill => {
            addSkillToEditList(skill);
        });

        // Set up event listeners for the skills input
        const skillsInput = document.getElementById('skills-input');
        const addSkillBtn = document.getElementById('add-skill-btn');

        // Remove existing event listeners
        skillsInput.removeEventListener('keydown', handleSkillInputKeydown);
        addSkillBtn.removeEventListener('click', handleAddSkillBtnClick);

        // Add new event listeners
        skillsInput.addEventListener('keydown', handleSkillInputKeydown);
        addSkillBtn.addEventListener('click', handleAddSkillBtnClick);
    } else if (type === 'scope') {
        console.log("Opening scope modal with current values:", jobDetails.scope);

        // Set description
        document.getElementById('edit-scope').value = jobDetails.scope.description;

        // Set project size if it exists
        if (jobDetails.scope.size) {
            const sizeRadio = document.getElementById(`size-${jobDetails.scope.size}`);
            if (sizeRadio) {
                sizeRadio.checked = true;
                console.log(`Set project size radio to ${jobDetails.scope.size}`);
            } else {
                console.log(`Could not find size radio for ${jobDetails.scope.size}`);
            }
        } else {
            // Uncheck all size options
            const sizeOptions = document.getElementsByName('project-size');
            sizeOptions.forEach(option => {
                option.checked = false;
            });
            console.log("Unchecked all size options");
        }

        // Set duration
        document.getElementById('scope-duration-value').value = jobDetails.scope.duration.value;
        document.getElementById('scope-duration-unit').value = jobDetails.scope.duration.unit;
        console.log(`Set duration to ${jobDetails.scope.duration.value} ${jobDetails.scope.duration.unit}`);

        // Set experience level
        document.getElementById('scope-experience').value = jobDetails.scope.experienceLevel;
        console.log(`Set experience level to ${jobDetails.scope.experienceLevel}`);

        // Set hiring preference
        document.getElementById('scope-hiring').value = jobDetails.scope.hiringPreference;
        console.log(`Set hiring preference to ${jobDetails.scope.hiringPreference}`);
    } else if (type === 'budget') {
        console.log("Opening budget modal with current values:", jobDetails.budget);

        // Set budget type
        const budgetTypeRadio = document.getElementById(`budget-${jobDetails.budget.type}`);
        if (budgetTypeRadio) {
            budgetTypeRadio.checked = true;
            console.log(`Set budget type radio to ${jobDetails.budget.type}`);
        } else {
            console.log(`Could not find budget type radio for ${jobDetails.budget.type}`);
        }

        // Toggle appropriate fields based on budget type
        toggleBudgetFields(jobDetails.budget.type);

        // Set budget values
        if (jobDetails.budget.type === 'fixed') {
            document.getElementById('budget-fixed-amount').value = jobDetails.budget.amount;
            console.log(`Set fixed budget amount to ${jobDetails.budget.amount}`);
        } else {
            document.getElementById('budget-min').value = jobDetails.budget.min;
            document.getElementById('budget-max').value = jobDetails.budget.max;
            console.log(`Set hourly budget range to ${jobDetails.budget.min} - ${jobDetails.budget.max}`);
        }

        // Add event listeners for budget type radio buttons
        document.getElementById('budget-hourly').addEventListener('change', function() {
            if (this.checked) toggleBudgetFields('hourly');
        });

        document.getElementById('budget-fixed').addEventListener('change', function() {
            if (this.checked) toggleBudgetFields('fixed');
        });

        console.log("Opening budget modal with values:", jobDetails.budget);
    }

    // Show modal with animation
    modal.style.display = 'flex';
    const modalContent = modal.querySelector('.modal-content');
    animateElement(modalContent, 'modal-in', 400);
}

// Close modal with animation
function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    const modalContent = modal.querySelector('.modal-content');

    animateElement(modalContent, 'modal-out', 300).then(() => {
        modal.style.display = 'none';
    });
}

// Add keyframe animations
const style = document.createElement('style');
style.textContent = `
    @keyframes modal-in {
        from { opacity: 0; transform: scale(0.8) translateY(30px); }
        to { opacity: 1; transform: scale(1) translateY(0); }
    }

    @keyframes modal-out {
        from { opacity: 1; transform: scale(1) translateY(0); }
        to { opacity: 0; transform: scale(0.8) translateY(30px); }
    }

    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }
`;
document.head.appendChild(style);

// Helper function to add a skill to the edit list
function addSkillToEditList(skill) {
    if (!skill || skill.trim() === '') return;

    // Check if skill already exists
    const existingSkills = Array.from(document.querySelectorAll('#edit-skills-list .skill-tag'))
        .map(tag => tag.dataset.skill);

    if (existingSkills.includes(skill.trim())) {
        // Highlight the existing skill briefly
        const existingTag = document.querySelector(`#edit-skills-list .skill-tag[data-skill="${skill.trim()}"]`);
        existingTag.style.animation = 'pulse 0.5s';
        setTimeout(() => {
            existingTag.style.animation = '';
        }, 500);
        return;
    }

    const skillsList = document.getElementById('edit-skills-list');
    const skillTag = document.createElement('span');
    skillTag.className = 'skill-tag';
    skillTag.textContent = skill.trim();
    skillTag.dataset.skill = skill.trim();
    skillTag.addEventListener('click', function() {
        this.remove();
    });

    skillsList.appendChild(skillTag);

    // Clear the input field
    document.getElementById('skills-input').value = '';
}

// Helper function to add a skill from suggestion
function addSkillFromSuggestion(skill) {
    addSkillToEditList(skill);

    // Add focus to the input field
    document.getElementById('skills-input').focus();
}

// Handle keydown event for skills input
function handleSkillInputKeydown(event) {
    if (event.key === 'Enter') {
        event.preventDefault();
        const skill = event.target.value.trim();
        if (skill) {
            addSkillToEditList(skill);
        }
    }
}

// Handle click event for add skill button
function handleAddSkillBtnClick() {
    const skillsInput = document.getElementById('skills-input');
    const skill = skillsInput.value.trim();
    if (skill) {
        addSkillToEditList(skill);
    }
}

// Function to toggle budget fields based on budget type
function toggleBudgetFields(type) {
    const hourlyFields = document.getElementById('hourly-rate-fields');
    const fixedFields = document.getElementById('fixed-price-fields');

    if (type === 'hourly') {
        hourlyFields.style.display = 'block';
        fixedFields.style.display = 'none';
    } else {
        hourlyFields.style.display = 'none';
        fixedFields.style.display = 'block';
    }
}

// Function to toggle between predefined and custom category
function toggleCategorySelectionType(type) {
    const predefinedCategory = document.getElementById('predefined-category');
    const customCategory = document.getElementById('custom-category');
    const toggleButtons = document.querySelectorAll('.selection-type-toggle button');

    // Update toggle buttons
    toggleButtons.forEach(button => {
        button.classList.remove('active');
    });

    // Find the clicked button and make it active
    const activeButton = document.querySelector(`.selection-toggle[onclick*="${type}"]`);
    if (activeButton) {
        activeButton.classList.add('active');
    }

    if (type === 'predefined') {
        predefinedCategory.style.display = 'block';
        customCategory.style.display = 'none';
    } else {
        predefinedCategory.style.display = 'none';
        customCategory.style.display = 'block';
    }
}

// Function to toggle between predefined and custom specialty
function toggleSpecialtySelectionType(type) {
    const predefinedSpecialty = document.getElementById('predefined-specialty');
    const customSpecialty = document.getElementById('custom-specialty');
    const toggleButtons = document.querySelectorAll('.selection-type-toggle:nth-child(2) button');

    // Update toggle buttons
    toggleButtons.forEach(button => {
        button.classList.remove('active');
    });

    // Find the clicked button and make it active
    const activeButton = document.querySelector(`.selection-toggle[onclick*="${type}"]`);
    if (activeButton) {
        activeButton.classList.add('active');
    }

    if (type === 'predefined') {
        predefinedSpecialty.style.display = 'block';
        customSpecialty.style.display = 'none';
    } else {
        predefinedSpecialty.style.display = 'none';
        customSpecialty.style.display = 'block';
    }
}

// Function to update specialties based on selected category
function updateSpecialties() {
    const categorySelect = document.getElementById('edit-category');
    const selectedCategory = categorySelect.options[categorySelect.selectedIndex].textContent;

    console.log("Updating specialties for category:", selectedCategory);

    // Hide all specialty groups first
    const specialtyGroups = document.querySelectorAll('.specialty-group');
    specialtyGroups.forEach(group => {
        group.style.display = 'none';
    });

    // Show only the specialty group that matches the selected category
    const matchingGroup = document.querySelector(`.specialty-group[data-category="${selectedCategory}"]`);
    if (matchingGroup) {
        console.log("Found matching specialty group:", matchingGroup.getAttribute('label'));
        matchingGroup.style.display = 'block';

        // Select the first option in the matching group
        const firstOption = matchingGroup.querySelector('option');
        if (firstOption) {
            firstOption.selected = true;
            console.log("Selected first specialty option:", firstOption.textContent);
        }
    } else {
        console.log("No matching specialty group found for category:", selectedCategory);
    }
}

// Removed scope templates function

// Save edits with validation
function saveEdit(type) {
    if (type === 'title') {
        const newTitle = document.getElementById('edit-title').value.trim();
        if (!newTitle) {
            showToast('Please enter a job title', 'error');
            return;
        }
        jobDetails.title = newTitle;
        document.getElementById('job-title').textContent = newTitle;
        closeModal('titleModal');
        showToast('Job title updated successfully');

    } else if (type === 'category') {
        // Check if using predefined or custom category
        const isPredefinedCategory = document.getElementById('predefined-category').style.display !== 'none';
        const isPredefinedSpecialty = document.getElementById('predefined-specialty').style.display !== 'none';

        let categoryValue, specialtyValue;

        // Get category value
        if (isPredefinedCategory) {
            const categorySelect = document.getElementById('edit-category');
            // Use textContent instead of value to get the display text
            categoryValue = categorySelect.options[categorySelect.selectedIndex].textContent;
            console.log("Selected category:", categoryValue);
        } else {
            const customCategoryInput = document.getElementById('custom-category-input');
            categoryValue = customCategoryInput.value.trim();

            if (!categoryValue) {
                showToast('Please enter a custom category', 'error');
                return;
            }
        }

        // Get specialty value
        if (isPredefinedSpecialty) {
            const specialtySelect = document.getElementById('edit-specialty');
            // Use textContent instead of value to get the display text
            specialtyValue = specialtySelect.options[specialtySelect.selectedIndex].textContent;
            console.log("Selected specialty:", specialtyValue);
        } else {
            const customSpecialtyInput = document.getElementById('custom-specialty-input');
            specialtyValue = customSpecialtyInput.value.trim();

            if (!specialtyValue) {
                showToast('Please enter a custom specialty', 'error');
                return;
            }
        }

        // Update job details
        jobDetails.category = categoryValue;
        jobDetails.specialty = specialtyValue;

        console.log("Updating job details - Category:", categoryValue, "Specialty:", specialtyValue);

        // Update display
        document.getElementById('job-category').textContent = jobDetails.category;
        document.getElementById('job-specialty').textContent = jobDetails.specialty;

        console.log("Updated display - Category:", document.getElementById('job-category').textContent,
                   "Specialty:", document.getElementById('job-specialty').textContent);

        closeModal('categoryModal');
        showToast('Job category and specialty updated successfully');

    } else if (type === 'description') {
        const newDescription = document.getElementById('edit-description').value.trim();
        if (!newDescription) {
            showToast('Please enter a job description', 'error');
            return;
        }
        jobDetails.description = newDescription;
        document.getElementById('job-description').value = newDescription;
        closeModal('descriptionModal');
        showToast('Job description updated successfully');

    } else if (type === 'skills') {
        // Get all skills from the edit list
        const skillTags = document.querySelectorAll('#edit-skills-list .skill-tag');
        const updatedSkills = Array.from(skillTags).map(tag => tag.dataset.skill);

        if (updatedSkills.length === 0) {
            showToast('Please add at least one skill', 'error');
            return;
        }

        // Update job details
        jobDetails.skills = updatedSkills;

        // Update the skills display in the main view
        const skillsContainer = document.querySelector('.skills-list');
        skillsContainer.innerHTML = '';
        updatedSkills.forEach(skill => {
            const skillTag = document.createElement('span');
            skillTag.className = 'skill-tag';
            skillTag.textContent = skill;
            skillsContainer.appendChild(skillTag);
        });

        closeModal('skillsModal');
        showToast('Skills updated successfully');

    } else if (type === 'scope') {
        // Get description
        const newDescription = document.getElementById('edit-scope').value.trim();

        // Get project size
        const sizeOptions = document.getElementsByName('project-size');
        let selectedSize = ''; // Default is empty
        for (const option of sizeOptions) {
            if (option.checked) {
                selectedSize = option.value;
                break;
            }
        }

        // Get duration
        const durationValue = parseInt(document.getElementById('scope-duration-value').value) || 0;
        const durationUnit = document.getElementById('scope-duration-unit').value;

        // Get experience level
        const experienceLevel = document.getElementById('scope-experience').value;

        // Get hiring preference
        const hiringPreference = document.getElementById('scope-hiring').value;

        // Update job details
        jobDetails.scope = {
            description: newDescription,
            size: selectedSize,
            duration: {
                value: durationValue,
                unit: durationUnit
            },
            experienceLevel: experienceLevel,
            hiringPreference: hiringPreference
        };

        console.log("Updated job scope details:", jobDetails.scope);

        // Update the display
        document.getElementById('job-scope-description').textContent = newDescription;

        // Update size display with proper capitalization
        const sizeDisplay = selectedSize ? (selectedSize.charAt(0).toUpperCase() + selectedSize.slice(1)) : '0';
        document.getElementById('job-scope-size').textContent = sizeDisplay;

        // Update duration display
        let durationDisplay = '0';
        if (durationValue > 0 && durationUnit) {
            const unitDisplay = durationValue === 1 ? durationUnit.slice(0, -1) : durationUnit;
            durationDisplay = `${durationValue} ${unitDisplay}`;
        }
        document.getElementById('job-scope-duration').textContent = durationDisplay;

        // Update experience level display with proper capitalization
        let experienceDisplay = '0';
        if (experienceLevel) {
            switch(experienceLevel) {
                case 'entry':
                    experienceDisplay = 'Entry Level';
                    break;
                case 'intermediate':
                    experienceDisplay = 'Intermediate';
                    break;
                case 'expert':
                    experienceDisplay = 'Expert';
                    break;
                default:
                    experienceDisplay = experienceLevel;
            }
        }
        document.getElementById('job-scope-experience').textContent = experienceDisplay;

        // Update hiring preference display
        let hiringDisplay = '0';
        if (hiringPreference) {
            switch(hiringPreference) {
                case 'individual':
                    hiringDisplay = 'Individual Freelancer';
                    break;
                case 'agency':
                    hiringDisplay = 'Agency';
                    break;
                case 'any':
                    hiringDisplay = 'No Preference';
                    break;
                default:
                    hiringDisplay = hiringPreference;
            }
        }
        document.getElementById('job-scope-hiring').textContent = hiringDisplay;

        closeModal('scopeModal');
        showToast('Project scope updated successfully');

    } else if (type === 'budget') {
        // Get budget type
        const budgetTypeOptions = document.getElementsByName('budget-type');
        let selectedBudgetType = 'fixed'; // Default
        for (const option of budgetTypeOptions) {
            if (option.checked) {
                selectedBudgetType = option.value;
                break;
            }
        }

        let budgetDisplay = '';
        let updatedBudget = { type: selectedBudgetType };

        if (selectedBudgetType === 'hourly') {
            // Get hourly rate inputs
            const minRate = parseInt(document.getElementById('budget-min').value) || 0;
            const maxRate = parseInt(document.getElementById('budget-max').value) || 0;

            // Validate that max is not less than min
            if (minRate > maxRate && maxRate !== 0) {
                showToast('Minimum rate cannot be greater than maximum rate', 'error');
                return;
            }

            updatedBudget.min = minRate;
            updatedBudget.max = maxRate;

            // Display appropriate message based on values
            if (minRate === 0 && maxRate === 0) {
                budgetDisplay = '$0';
            } else if (minRate === 0) {
                budgetDisplay = `Up to $${maxRate} / hour`;
            } else if (maxRate === 0) {
                budgetDisplay = `From $${minRate} / hour`;
            } else {
                budgetDisplay = `$${minRate} - $${maxRate} / hour`;
            }
        } else {
            // Get fixed price input
            const fixedAmount = parseInt(document.getElementById('budget-fixed-amount').value) || 0;

            updatedBudget.amount = fixedAmount;

            // Display appropriate message based on value
            if (fixedAmount === 0) {
                budgetDisplay = '$0';
            } else {
                budgetDisplay = `$${fixedAmount} (Fixed Price)`;
            }

            console.log("Updated budget to fixed price:", updatedBudget);
        }

        // Update job details
        jobDetails.budget = updatedBudget;
        console.log("Updated job budget details:", jobDetails.budget);

        // Update display
        document.getElementById('job-budget').textContent = budgetDisplay;
        closeModal('budgetModal');
        showToast('Budget updated successfully');

    }

    // Update job status
    updateJobStatus();
}

// Toast notification system
function showToast(message, type = 'success') {
    // Remove existing toasts
    const existingToasts = document.querySelectorAll('.toast-notification');
    existingToasts.forEach(toast => {
        document.body.removeChild(toast);
    });

    // Create new toast
    const toast = document.createElement('div');
    toast.className = `toast-notification toast-${type}`;
    toast.innerHTML = `
        <div class="toast-icon">
            <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'}"></i>
        </div>
        <div class="toast-message">${message}</div>
    `;

    // Add toast styles if not already added
    if (!document.getElementById('toast-styles')) {
        const toastStyles = document.createElement('style');
        toastStyles.id = 'toast-styles';
        toastStyles.textContent = `
            .toast-notification {
                position: fixed;
                bottom: 20px;
                right: 20px;
                background: white;
                border-radius: 8px;
                padding: 12px 20px;
                display: flex;
                align-items: center;
                box-shadow: 0 5px 15px rgba(0,0,0,0.2);
                z-index: 9999;
                animation: toast-in 0.3s forwards, toast-out 0.3s forwards 3s;
                max-width: 300px;
            }

            .toast-success {
                border-left: 4px solid #4CAF50;
            }

            .toast-error {
                border-left: 4px solid #F44336;
            }

            .toast-icon {
                margin-right: 12px;
                font-size: 20px;
            }

            .toast-success .toast-icon {
                color: #4CAF50;
            }

            .toast-error .toast-icon {
                color: #F44336;
            }

            .toast-message {
                font-size: 14px;
                color: #333;
            }

            @keyframes toast-in {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }

            @keyframes toast-out {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(toastStyles);
    }

    document.body.appendChild(toast);

    // Remove toast after animation
    setTimeout(() => {
        if (document.body.contains(toast)) {
            document.body.removeChild(toast);
        }
    }, 3300);
}

// Update job status based on completeness
function updateJobStatus() {
    const requiredFields = [
        jobDetails.title,
        jobDetails.category,
        jobDetails.description,
        jobDetails.skills.length > 0 ? 'skills' : ''
    ];

    const completedFields = requiredFields.filter(field => field && field.toString().trim() !== '').length;
    const totalFields = requiredFields.length;
    const completionPercentage = Math.floor((completedFields / totalFields) * 100);

    const statusElement = document.querySelector('.job-status');

    if (completionPercentage === 100) {
        statusElement.textContent = 'Ready to Post';
        statusElement.className = 'job-status ready';
    } else if (completionPercentage >= 60) {
        statusElement.textContent = 'In Progress';
        statusElement.className = 'job-status progress';
    } else {
        statusElement.textContent = 'Draft';
        statusElement.className = 'job-status draft';
    }

    // Pulse animation on status change
    animateElement(statusElement, 'pulse', 600);
}

// Submit job with validation
function submitJob() {
    // Get all job details
    const description = document.getElementById('job-description').value.trim();
    jobDetails.description = description;

    // Validate required fields
    if (!jobDetails.title || !jobDetails.category || !description) {
        showToast('Please fill in all required fields', 'error');
        return;
    }

    // Create form data to submit
    const formData = new FormData();

    // Basic job details
    formData.append('title', jobDetails.title);
    formData.append('description', description);
    formData.append('category', jobDetails.category);
    formData.append('specialty', jobDetails.specialty || '');

    // Skills (convert array to JSON string)
    formData.append('skills', JSON.stringify(jobDetails.skills || []));

    // Scope details - make sure field names match what the server expects
    if (jobDetails.scope) {
        formData.append('project_size', jobDetails.scope.size || '');
        formData.append('project_description', jobDetails.scope.description || '');

        // Handle duration properly
        if (jobDetails.scope.duration) {
            if (typeof jobDetails.scope.duration === 'object') {
                // If it's an object with value and unit
                const durationStr = `${jobDetails.scope.duration.value} ${jobDetails.scope.duration.unit}`;
                formData.append('duration', durationStr);
            } else {
                // If it's already a string
                formData.append('duration', jobDetails.scope.duration);
            }
        } else {
            formData.append('duration', '');
        }

        formData.append('experience_level', jobDetails.scope.experienceLevel || '');
        formData.append('hiring_preference', jobDetails.scope.hiringPreference || '');
    }

    // Budget details
    if (jobDetails.budget) {
        formData.append('budget_type', jobDetails.budget.type || 'fixed');
        formData.append('budget_amount', jobDetails.budget.amount || '0');
    } else {
        formData.append('budget_type', 'fixed');
        formData.append('budget_amount', '0');
    }

    // Add job_type field with a default value
    formData.append('job_type', jobDetails.job_type || 'One-time project');



    // Debug log
    console.log('Submitting job with details:', {
        title: jobDetails.title,
        description: description,
        category: jobDetails.category,
        specialty: jobDetails.specialty,
        skills: jobDetails.skills,
        scope: jobDetails.scope,
        budget: jobDetails.budget,
        job_type: jobDetails.job_type
    });

    // Show loading state
    const submitButton = document.querySelector('.btn-primary');
    const originalText = submitButton.innerHTML;
    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Submitting...';
    submitButton.disabled = true;

    // Send data to server
    fetch('/submit_job', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        console.log('Server response:', data);
        if (data.success) {
            showToast('Job posted successfully!');

            // Show success animation
            submitButton.innerHTML = '<i class="fas fa-check"></i> Posted Successfully!';
            submitButton.style.backgroundColor = '#4CAF50';

            // Redirect after delay
            setTimeout(() => {
                if (data.redirect) {
                    window.location.href = data.redirect;
                }
            }, 1500);
        } else {
            showToast('Error: ' + (data.error || 'An error occurred while submitting the job'), 'error');
            submitButton.innerHTML = originalText;
            submitButton.disabled = false;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('An error occurred while submitting the job', 'error');
        submitButton.innerHTML = originalText;
        submitButton.disabled = false;
    });
}

// Initialize the page
document.addEventListener('DOMContentLoaded', function() {
    console.log("DOM Content Loaded - Initializing page3.js");

    // Set initial job details
    jobDetails.title = document.getElementById('job-title').textContent;
    jobDetails.category = document.getElementById('job-category').textContent;
    jobDetails.specialty = document.getElementById('job-specialty').textContent;

    // Check if job_type is available in the page
    const jobTypeElement = document.getElementById('job-type');
    if (jobTypeElement && jobTypeElement.textContent) {
        jobDetails.job_type = jobTypeElement.textContent;
    }

    // Check if we're in edit mode
    const isEditMode = document.querySelector('.btn-primary').textContent.includes('Update Job');

    // If in edit mode, pre-populate the description field if it exists in session
    if (isEditMode) {
        // Try to get description from session data
        const descriptionElement = document.getElementById('job-description');
        if (descriptionElement) {
            // If the description is empty, check if there's a data attribute with the session value
            if (!descriptionElement.value && descriptionElement.dataset.sessionValue) {
                descriptionElement.value = descriptionElement.dataset.sessionValue;
                jobDetails.description = descriptionElement.dataset.sessionValue;
            }
        }

        // Try to get budget from session data
        const budgetElement = document.getElementById('job-budget');
        if (budgetElement) {
            const budgetText = budgetElement.textContent;
            if (budgetText.includes('Fixed Price')) {
                const amount = parseInt(budgetText.replace(/[^0-9]/g, '')) || 0;
                jobDetails.budget = {
                    type: 'fixed',
                    amount: amount
                };
            } else if (budgetText.includes('-')) {
                const parts = budgetText.split('-');
                const min = parseInt(parts[0].replace(/[^0-9]/g, '')) || 0;
                const max = parseInt(parts[1].replace(/[^0-9]/g, '')) || 0;
                jobDetails.budget = {
                    type: 'hourly',
                    min: min,
                    max: max
                };
            }
        }

        // Update the submit button text
        const submitButton = document.querySelector('.btn-primary');
        if (submitButton) {
            submitButton.innerHTML = '<i class="fas fa-save"></i> Update Job';
        }
    }

    // Log the initial job details for debugging
    console.log("Initialized job details:", {
        title: jobDetails.title,
        category: jobDetails.category,
        specialty: jobDetails.specialty,
        job_type: jobDetails.job_type,
        isEditMode: isEditMode
    });

    // Add event listeners to category edit button
    const categoryEditButton = document.querySelector('.edit-button[onclick*="category"]');
    if (categoryEditButton) {
        console.log("Found category edit button, adding click event listener");
        categoryEditButton.addEventListener('click', function() {
            console.log("Category edit button clicked");
        });
    }

    // Add event listeners to Save Changes button in category modal
    const categorySaveButton = document.querySelector('#categoryModal .btn-primary');
    if (categorySaveButton) {
        console.log("Found category save button, adding click event listener");
        categorySaveButton.addEventListener('click', function() {
            console.log("Category save button clicked");
        });
    }

    // Add escape key listener to close modals
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            const openModals = document.querySelectorAll('.modal[style*="display: flex"]');
            openModals.forEach(modal => {
                closeModal(modal.id);
            });
        }
    });

    // Initialize job status
    updateJobStatus();
});
