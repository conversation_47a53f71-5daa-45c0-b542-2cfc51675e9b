<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Job Details - {{ job.title }}</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #004aad;
            --primary-pink: #cd208b;
            --neutral-100: #f9fafc;
            --neutral-200: #f0f2f5;
            --neutral-300: #e0e4e8;
            --neutral-400: #c5cad3;
            --neutral-500: #8a94a6;
            --neutral-600: #5e6577;
            --neutral-700: #3e4555;
            --neutral-800: #2a2f3c;
            --neutral-900: #1a1d27;
            --success: #28a745;
            --warning: #ffc107;
            --danger: #dc3545;
            --info: #17a2b8;
            --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.1);
            --radius-sm: 4px;
            --radius-md: 8px;
            --radius-lg: 16px;
            --radius-xl: 24px;
            --radius-full: 9999px;
            --spacing-1: 0.25rem;
            --spacing-2: 0.5rem;
            --spacing-3: 0.75rem;
            --spacing-4: 1rem;
            --spacing-5: 1.25rem;
            --spacing-6: 1.5rem;
            --spacing-8: 2rem;
            --spacing-10: 2.5rem;
            --spacing-12: 3rem;
            --spacing-16: 4rem;
            --spacing-20: 5rem;
            --font-size-xs: 0.75rem;
            --font-size-sm: 0.875rem;
            --font-size-base: 1rem;
            --font-size-lg: 1.125rem;
            --font-size-xl: 1.25rem;
            --font-size-2xl: 1.5rem;
            --font-size-3xl: 1.875rem;
            --font-size-4xl: 2.25rem;
            --font-size-5xl: 3rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', system-ui, -apple-system, sans-serif;
        }

        body {
            background-color: var(--neutral-100);
            color: var(--neutral-800);
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: var(--spacing-4);
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-4) 0;
            margin-bottom: var(--spacing-6);
            border-bottom: 1px solid var(--neutral-300);
        }

        .header h1 {
            font-size: var(--font-size-2xl);
            color: var(--primary-blue);
            font-weight: 600;
        }

        .back-button {
            display: flex;
            align-items: center;
            gap: var(--spacing-2);
            color: var(--neutral-600);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .back-button:hover {
            color: var(--primary-blue);
            transform: translateX(-3px);
        }

        .job-details-container {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: var(--spacing-6);
        }

        .job-main {
            background-color: white;
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-md);
            padding: var(--spacing-6);
        }

        .job-sidebar {
            background-color: white;
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-md);
            padding: var(--spacing-6);
            height: fit-content;
        }

        .job-title {
            font-size: var(--font-size-2xl);
            color: var(--neutral-800);
            margin-bottom: var(--spacing-2);
        }

        .job-meta {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-3);
            margin-bottom: var(--spacing-4);
        }

        .job-tag {
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-1);
            padding: var(--spacing-1) var(--spacing-3);
            background-color: var(--neutral-200);
            border-radius: var(--radius-full);
            font-size: var(--font-size-sm);
            color: var(--neutral-700);
        }

        .job-tag.fixed-price {
            background-color: rgba(0, 74, 173, 0.1);
            color: var(--primary-blue);
        }

        .job-tag.hourly {
            background-color: rgba(205, 32, 139, 0.1);
            color: var(--primary-pink);
        }

        .job-tag.status {
            background-color: rgba(40, 167, 69, 0.1);
            color: var(--success);
        }

        .job-description {
            margin-bottom: var(--spacing-6);
        }

        .job-description h2 {
            font-size: var(--font-size-xl);
            color: var(--neutral-800);
            margin-bottom: var(--spacing-3);
            font-weight: 600;
        }

        .job-description p {
            color: var(--neutral-700);
            margin-bottom: var(--spacing-4);
            line-height: 1.7;
        }

        .job-section {
            margin-bottom: var(--spacing-6);
        }

        .job-section h2 {
            font-size: var(--font-size-xl);
            color: var(--neutral-800);
            margin-bottom: var(--spacing-3);
            font-weight: 600;
        }

        .job-section p {
            color: var(--neutral-700);
            margin-bottom: var(--spacing-4);
        }

        .job-details-list {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-4);
        }

        .detail-item {
            display: flex;
            flex-direction: column;
        }

        .detail-label {
            font-size: var(--font-size-sm);
            color: var(--neutral-500);
            margin-bottom: var(--spacing-1);
        }

        .detail-value {
            font-weight: 500;
            color: var(--neutral-800);
        }

        .sidebar-section {
            margin-bottom: var(--spacing-6);
        }

        .sidebar-section h3 {
            font-size: var(--font-size-lg);
            color: var(--neutral-800);
            margin-bottom: var(--spacing-3);
            font-weight: 600;
        }

        .action-button {
            display: block;
            width: 100%;
            padding: var(--spacing-3) var(--spacing-4);
            background-color: var(--primary-blue);
            color: white;
            border: none;
            border-radius: var(--radius-md);
            font-weight: 500;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            margin-bottom: var(--spacing-3);
        }

        .action-button:hover {
            background-color: #003d91;
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .action-button.secondary {
            background-color: white;
            color: var(--primary-blue);
            border: 1px solid var(--primary-blue);
        }

        .action-button.secondary:hover {
            background-color: rgba(0, 74, 173, 0.05);
        }

        .applications-section {
            margin-top: var(--spacing-8);
        }

        .applications-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-4);
        }

        .applications-header h2 {
            font-size: var(--font-size-xl);
            color: var(--neutral-800);
            font-weight: 600;
        }

        .applications-count {
            background-color: var(--primary-blue);
            color: white;
            padding: var(--spacing-1) var(--spacing-3);
            border-radius: var(--radius-full);
            font-size: var(--font-size-sm);
            font-weight: 500;
        }

        .applications-list {
            display: grid;
            gap: var(--spacing-4);
        }

        .application-card {
            background-color: white;
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-md);
            padding: var(--spacing-4);
            display: grid;
            grid-template-columns: auto 1fr auto;
            gap: var(--spacing-4);
            align-items: start;
        }

        .applicant-photo {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid var(--neutral-300);
        }

        .applicant-info h3 {
            font-size: var(--font-size-lg);
            color: var(--neutral-800);
            margin-bottom: var(--spacing-1);
            font-weight: 600;
        }

        .applicant-meta {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-2);
            margin-bottom: var(--spacing-2);
        }

        .applicant-tag {
            font-size: var(--font-size-xs);
            color: var(--neutral-600);
            background-color: var(--neutral-200);
            padding: var(--spacing-1) var(--spacing-2);
            border-radius: var(--radius-full);
        }

        .applicant-intro {
            color: var(--neutral-700);
            font-size: var(--font-size-sm);
            margin-bottom: var(--spacing-2);
            line-height: 1.6;
        }

        .application-actions {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-2);
        }

        .application-button {
            padding: var(--spacing-2) var(--spacing-3);
            border-radius: var(--radius-md);
            font-size: var(--font-size-sm);
            font-weight: 500;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .application-button.primary {
            background-color: var(--primary-blue);
            color: white;
            border: none;
        }

        .application-button.primary:hover {
            background-color: #003d91;
        }

        .application-button.secondary {
            background-color: white;
            color: var(--primary-blue);
            border: 1px solid var(--primary-blue);
        }

        .application-button.secondary:hover {
            background-color: rgba(0, 74, 173, 0.05);
        }

        @media (max-width: 768px) {
            .job-details-container {
                grid-template-columns: 1fr;
            }

            .job-details-list {
                grid-template-columns: 1fr;
            }

            .application-card {
                grid-template-columns: 1fr;
                text-align: center;
            }

            .applicant-photo {
                margin: 0 auto var(--spacing-3);
            }

            .applicant-meta {
                justify-content: center;
            }

            .application-actions {
                margin-top: var(--spacing-3);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <a href="{{ url_for('client_page') }}" class="back-button">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </a>
            <h1>Job Details</h1>
        </div>

        <div class="job-details-container">
            <div class="job-main">
                <h2 class="job-title">{{ job.title }}</h2>
                <div class="job-meta">
                    <span class="job-tag {{ 'fixed-price' if job.job_type == 'one-time' else 'hourly' }}">
                        <i class="fas fa-{{ 'tag' if job.job_type == 'one-time' else 'clock' }}"></i>
                        {{ 'Fixed Price' if job.job_type == 'one-time' else 'Hourly Rate' }}
                    </span>
                    <span class="job-tag">
                        <i class="fas fa-folder"></i>
                        {{ job.category }}
                    </span>
                    <span class="job-tag">
                        <i class="fas fa-calendar-alt"></i>
                        Posted {{ job.created_at_formatted }}
                    </span>
                    <span class="job-tag status">
                        <i class="fas fa-circle"></i>
                        {{ job.status or 'Open' }}
                    </span>
                </div>

                <div class="job-description">
                    <h2>Job Description</h2>
                    <p>{{ job.description }}</p>
                </div>

                <div class="job-section">
                    <h2>Job Details</h2>
                    <div class="job-details-list">
                        <div class="detail-item">
                            <span class="detail-label">Budget</span>
                            <span class="detail-value">
                                {% if job.budget_type == 'fixed' %}
                                    ${{ job.budget_amount }}
                                {% else %}
                                    ${{ job.budget_amount }}/hour
                                {% endif %}
                            </span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Project Size</span>
                            <span class="detail-value">{{ job.project_size }}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Duration</span>
                            <span class="detail-value">{{ job.duration }}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Experience Level</span>
                            <span class="detail-value">{{ job.experience_level }}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Category</span>
                            <span class="detail-value">{{ job.category }}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Specialty</span>
                            <span class="detail-value">{{ job.specialty }}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Skills</span>
                            <span class="detail-value">{{ job.skills }}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Applications</span>
                            <span class="detail-value">{{ job.application_count or 0 }}</span>
                        </div>
                    </div>
                </div>

                {% if applications %}
                <div class="applications-section">
                    <div class="applications-header">
                        <h2>Applications</h2>
                        <span class="applications-count">{{ applications|length }}</span>
                    </div>
                    <div class="applications-list">
                        {% for app in applications %}
                        <div class="application-card">
                            <img src="{{ url_for('static', filename='uploads/' + app.profile_photo) if app.profile_photo else url_for('static', filename='img/default-avatar.png') }}" alt="{{ app.first_name }} {{ app.last_name }}" class="applicant-photo">
                            <div class="applicant-info">
                                <h3>{{ app.first_name }} {{ app.last_name }}</h3>
                                <div class="applicant-meta">
                                    <span class="applicant-tag">{{ app.position }}</span>
                                    <span class="applicant-tag">{{ app.country }}</span>
                                    <span class="applicant-tag">${{ app.hourly_rate }}/hr</span>
                                    <span class="applicant-tag">{{ app.expertise }}</span>
                                </div>
                                <p class="applicant-intro">{{ app.introduction[:150] }}{% if app.introduction|length > 150 %}...{% endif %}</p>
                                <div class="applicant-meta">
                                    <span class="applicant-tag">Applied {{ app.created_at_formatted }}</span>
                                    <span class="applicant-tag">Status: {{ app.status or 'Pending' }}</span>
                                </div>
                            </div>
                            <div class="application-actions">
                                <a href="{{ url_for('messages') }}?genius_id={{ app.genius_id }}" class="application-button primary">
                                    <i class="fas fa-comment"></i> Message
                                </a>
                                <button class="application-button secondary" onclick="viewProfile({{ app.genius_id }})">
                                    <i class="fas fa-user"></i> View Profile
                                </button>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% else %}
                <div class="applications-section">
                    <div class="applications-header">
                        <h2>Applications</h2>
                        <span class="applications-count">0</span>
                    </div>
                    <div style="text-align: center; padding: 2rem; background-color: var(--neutral-100); border-radius: var(--radius-lg);">
                        <i class="fas fa-inbox" style="font-size: 3rem; color: var(--neutral-400); margin-bottom: 1rem;"></i>
                        <h3 style="margin-bottom: 0.5rem; color: var(--neutral-700);">No applications yet</h3>
                        <p style="color: var(--neutral-600);">When geniuses apply to your job, they'll appear here.</p>
                    </div>
                </div>
                {% endif %}
            </div>

            <div class="job-sidebar">
                <div class="sidebar-section">
                    <h3>Job Actions</h3>
                    <a href="{{ url_for('page1') }}?edit_job_id={{ job.id }}" class="action-button">
                        <i class="fas fa-edit"></i> Edit Job
                    </a>
                    <button class="action-button secondary" onclick="closeJob({{ job.id }})">
                        <i class="fas fa-times-circle"></i> Close Job
                    </button>
                </div>

                <div class="sidebar-section">
                    <h3>Job Status</h3>
                    <div class="detail-item" style="margin-bottom: var(--spacing-3);">
                        <span class="detail-label">Current Status</span>
                        <span class="detail-value">{{ job.status or 'Open' }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Applications</span>
                        <span class="detail-value">{{ job.application_count or 0 }} received</span>
                    </div>
                </div>

                <div class="sidebar-section">
                    <h3>Share Job</h3>
                    <div style="display: flex; gap: var(--spacing-2); margin-top: var(--spacing-3);">
                        <button class="action-button" style="margin: 0; flex: 1;" onclick="copyJobLink({{ job.id }})">
                            <i class="fas fa-copy"></i> Copy Link
                        </button>
                        <button class="action-button secondary" style="margin: 0; flex: 1;" onclick="shareJob({{ job.id }})">
                            <i class="fas fa-share-alt"></i> Share
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function viewProfile(geniusId) {
            // Implement view profile functionality
            alert('View profile functionality will be implemented soon.');
        }

        function closeJob(jobId) {
            if (confirm('Are you sure you want to close this job? This action cannot be undone.')) {
                // Implement close job functionality
                alert('Close job functionality will be implemented soon.');
            }
        }

        function copyJobLink(jobId) {
            const jobUrl = window.location.origin + '/job_details/' + jobId;
            navigator.clipboard.writeText(jobUrl).then(() => {
                alert('Job link copied to clipboard!');
            }).catch(err => {
                console.error('Could not copy text: ', err);
            });
        }

        function shareJob(jobId) {
            const jobUrl = window.location.origin + '/job_details/' + jobId;
            if (navigator.share) {
                navigator.share({
                    title: document.querySelector('.job-title').textContent,
                    text: 'Check out this job on GigGenius!',
                    url: jobUrl,
                }).then(() => {
                    console.log('Thanks for sharing!');
                }).catch(console.error);
            } else {
                copyJobLink(jobId);
            }
        }
    </script>
</body>
</html>
